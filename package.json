{"name": "block", "version": "1.3.2", "description": "Block Multipurpose Bootstrap 5 Theme", "main": "gulpfile.js", "scripts": {"dev": "gulp", "build": "gulp build", "format": "npx prettier --write ."}, "keywords": [], "author": "https://codescandy.com/", "license": "ISC", "homepage": "https://codescandy.com", "devDependencies": {"autoprefixer": "^10.4.15", "browser-sync": "^3.0.2", "cssnano": "^6.0.1", "del": "^6.1.1", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-cached": "^1.1.1", "gulp-file-include": "^2.3.0", "gulp-if": "^3.0.0", "gulp-npm-dist": "^1.0.4", "gulp-postcss": "^9.0.1", "gulp-purgecss": "^5.0.0", "gulp-replace": "^1.1.4", "gulp-sass": "^5.1.0", "gulp-terser": "^2.1.0", "gulp-uglify": "^3.0.2", "gulp-useref": "^3.1.3", "postcss": "^8.4.30", "prettier": "^3.0.3", "sass": "^1.67.0"}, "dependencies": {"bootstrap": "^5.3.6", "bootstrap-icons": "^1.11.1", "cleave.js": "^1.6.0", "embla-carousel": "^8.5.2", "embla-carousel-auto-scroll": "^8.5.2", "embla-carousel-autoplay": "^8.5.2", "glightbox": "^3.2.0", "headhesive": "^1.2.4", "intl-tel-input": "^23.0.11", "jarallax": "^2.1.4", "parallax-js": "^3.1.0", "plyr": "^3.7.8", "prismjs": "^1.29.0", "rellax": "^1.12.1", "scrollcue": "^2.0.0", "simplebar": "^6.2.5", "swiper": "^10.2.0"}}