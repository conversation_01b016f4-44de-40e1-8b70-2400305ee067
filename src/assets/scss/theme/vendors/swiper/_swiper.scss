@at-root {
  :root {
    --swiper-theme-color: #{var(--#{$prefix}primary)};
  }
}
.swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
  background: transparent;
  opacity: 0.3;
  border: 2px solid var(--#{$prefix}primary);
}

.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  border-color: var(--#{$prefix}primary);
}

[data-navigation="false"] .swiper-navigation {
  display: none;
  opacity: 0;
  visibility: hidden;
}

.swiper-pagination-numbers {
  width: 28px;
  height: 28px;
  display: inline-block;
  border-radius: 50%;
  background: $gray-200;
  margin: auto 2px;

  line-height: 28px;
  color: #000;
}

.swiper-pagination-numbers-active {
  opacity: 1;
  background: var(--#{$prefix}primary);
  color: var(--#{$prefix}white);
}

.swiper-button-prev {
  background-color: var(--#{$prefix}white);
  height: 40px;
  width: 40px;
  border-radius: $border-radius-pill;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  left: 0;
  right: 0;
  &:after {
    content: "\F12C";
    font-family: "bootstrap-icons";
    font-size: 24px;
  }
  &:hover {
    background-color: var(--#{$prefix}primary);
    color: var(--#{$prefix}white);
  }
}
.swiper-button-next {
  background-color: var(--#{$prefix}white);
  height: 40px;
  width: 40px;
  border-radius: $border-radius-pill;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  left: 0;
  right: 0;
  &:after {
    content: "\F135";
    font-family: "bootstrap-icons";
    font-size: 24px;
  }
  &:hover {
    background-color: var(--#{$prefix}primary);
    color: var(--#{$prefix}white);
  }
}
