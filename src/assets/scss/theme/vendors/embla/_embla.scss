.embla {
   margin: auto;
   .embla__viewport {
      overflow: hidden;
      padding: 2rem 0rem;
   }
   .embla__container {
      display: flex;
      touch-action: pan-x pinch-zoom;
      margin-top: calc(var(--slide-spacing) * -1);
      height: calc(var(--slide-spacing) + var(--slide-height));
      flex-direction: column;
      gap: 24px;
      /* Horizontal scrolling on mobile */
      @media (max-width: 768px) {
         flex-direction: row; /* Slide horizontally */
      }

      /* Vertical scrolling on desktop */
      @media (min-width: 769px) {
         flex-direction: column; /* Slide vertically */
      }

      .embla__slide {
         transform: translate3d(0, 0, 0);

         min-height: 0;
         padding-top: var(--slide-spacing);
         flex: 0 0 18%;
         @media (max-width: 1200px) {
            flex: 0 0 auto;
            width: 50%;
         }

         @media (max-width: 480px) {
            flex: 0 0 auto;
            width: 100%;
         }
      }
      .embla__slide__number {
         box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
         border-radius: 1.8rem;
         font-size: 4rem;
         font-weight: 600;
         display: flex;
         align-items: center;
         justify-content: center;
         height: 100%;
         user-select: none;
      }
   }
   .embla__dots {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      gap: 1rem;
      .embla__dot {
         background-color: $gray-800;
         touch-action: manipulation;
         display: inline-flex;
         text-decoration: none;
         cursor: pointer;
         border: 0;
         padding: 0;
         margin: 0;
         width: 0.75rem;
         height: 0.75rem;
         display: flex;
         align-items: center;
         justify-content: center;
         border-radius: 50%;
      }
      .embla__dot:after {
         box-shadow: none;
         width: 1rem;
         height: 1rem;
         border-radius: 50%;
         display: flex;
         align-items: center;
         content: "";
      }
      .embla__dot.is-selected {
         background-color: $primary;
      }
   }
}
