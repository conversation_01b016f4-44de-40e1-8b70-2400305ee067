<svg width="1728" height="244" viewBox="0 0 1728 244" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2249_670)">
<mask id="mask0_2249_670" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1728" height="799">
<path d="M0 0H1728V799C1728 799 1417 799 866 799C315 799 0 799 0 799V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_2249_670)">
<g opacity="0.3" filter="url(#filter0_f_2249_670)">
<ellipse cx="1266.5" cy="344" rx="460.5" ry="177" fill="#8B3DFF"/>
</g>
<g opacity="0.3" filter="url(#filter1_f_2249_670)">
<ellipse cx="371.5" cy="387" rx="371.5" ry="177" fill="#198754"/>
</g>
<g opacity="0.3" filter="url(#filter2_f_2249_670)">
<ellipse cx="743.5" cy="371" rx="371.5" ry="177" fill="#FFC107"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_f_2249_670" x="686" y="47" width="1161" height="594" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="60" result="effect1_foregroundBlur_2249_670"/>
</filter>
<filter id="filter1_f_2249_670" x="-120" y="90" width="983" height="594" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="60" result="effect1_foregroundBlur_2249_670"/>
</filter>
<filter id="filter2_f_2249_670" x="252" y="74" width="983" height="594" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="60" result="effect1_foregroundBlur_2249_670"/>
</filter>
<clipPath id="clip0_2249_670">
<rect width="1728" height="244" fill="white"/>
</clipPath>
</defs>
</svg>
