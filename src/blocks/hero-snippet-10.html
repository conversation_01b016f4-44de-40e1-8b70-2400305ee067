<!doctype html>
<html lang="en">
   <head>
      @@include("../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      <link rel="stylesheet" href="@@webRoot/node_modules/intl-tel-input/build/css/intlTelInput.min.css" />
      @@include("../partials/head/head-links.html")
      <title>Hero Snippet v9 - Responsive Website Template | Block</title>
   </head>

   <body>
      @@include("../partials/navbar-boxed.html",{ "classList": " navbar-light w-100 transparent" })
      <main>
         <!--Hero section-->
         <section
            class="pt-lg-10"
            style="
               background: url(../assets/images/mobile-app/curvlines.svg), linear-gradient(111.42deg, #4a00b7 0.42%, #c13dff 81.76%, #c13dff 96.06%);
               background-position: center;
               background-repeat: no-repeat;
               background-size: cover;
            ">
            <div class="container">
               <div class="pt-5">
                  <div class="row">
                     <div class="col-12">
                        <div class="row align-items-center gy-5 px-lg-6 position-relative">
                           <div class="col-md-6 col-12">
                              <div class="d-flex flex-column gap-7">
                                 <div class="d-flex flex-column gap-3 mb-lg-10">
                                    <div class="d-flex flex-row align-items-center">
                                       <a href="#!" class="bg-opacity-50 text-bg-primary border border-primary badge px-3 py-2 fw-medium rounded-pill fs-6">
                                          <span class="">Read our $6M Series A announcement</span>
                                          <span class="ms-1">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                <path
                                                   fill-rule="evenodd"
                                                   d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"></path>
                                             </svg>
                                          </span>
                                       </a>
                                    </div>
                                    <div class="d-flex flex-column gap-5">
                                       <div class="d-flex flex-column gap-3">
                                          <h1 class="mb-0 text-white-stable display-4">Master your money with confidence</h1>
                                          <p class="mb-0 text-white-50 lead">Navigate your finances with confidence. Track spending, budgets, investments, net worth.</p>
                                       </div>
                                       <div class="d-flex flex-row align-items-center gap-2">
                                          <a href="#!"><img src="../assets/images/mobile-app/playstore.svg" alt="playstore" /></a>
                                          <a href="#!"><img src="../assets/images/mobile-app/appstore.svg" alt="appstore" /></a>
                                          <img src="../assets/images/mobile-app/qr-code.svg" alt="qr-code" class="icon-md icon-shape" />
                                       </div>
                                    </div>
                                 </div>
                                 <div class="d-flex flex-row flex-wrap gap-2 align-items-center justify-content-center justify-content-md-start lh-1 position-lg-absolute bottom-0 mb-lg-6">
                                    <span class="text-white-stable fs-6">Great</span>
                                    <div>
                                       <img src="../assets/images/mobile-app/star.svg" alt="star" />
                                       <img src="../assets/images/mobile-app/star.svg" alt="star" />
                                       <img src="../assets/images/mobile-app/star.svg" alt="star" />
                                       <img src="../assets/images/mobile-app/star.svg" alt="star" />
                                       <img src="../assets/images/mobile-app/star-light.svg" alt="star" />
                                    </div>
                                    <span class="text-white-stable">6,427&nbsp;reviews on</span>
                                    <span class="lh-1">
                                       <span>
                                          <svg width="82" height="20" viewBox="0 0 82 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                             <g clip-path="url(#clip0_2350_3858)">
                                                <path
                                                   d="M21.3386 7.14197H29.5601V8.66726H26.3274V17.2417H24.5498V8.66726H21.3314V7.14197H21.3386ZM29.2088 9.92883H30.7284V11.3401H30.7571C30.8073 11.1405 30.9004 10.9481 31.0366 10.7627C31.1728 10.5774 31.3377 10.3992 31.5312 10.2496C31.7247 10.0928 31.9398 9.97159 32.1763 9.87181C32.4129 9.77915 32.6566 9.72926 32.9003 9.72926C33.0866 9.72926 33.2228 9.73638 33.2945 9.74351C33.3662 9.75064 33.4378 9.76489 33.5167 9.77202V11.3258C33.402 11.3044 33.2873 11.2902 33.1655 11.2759C33.0436 11.2617 32.9289 11.2545 32.8142 11.2545C32.5419 11.2545 32.2838 11.3116 32.0401 11.4185C31.7964 11.5254 31.5886 11.6893 31.4094 11.896C31.2302 12.1098 31.0868 12.3664 30.9793 12.68C30.8718 12.9937 30.8216 13.35 30.8216 13.7563V17.2345H29.2017V9.92883L29.2088 9.92883ZM40.964 17.2417H39.3728V16.2224H39.3441C39.1434 16.5931 38.8495 16.8853 38.4553 17.1062C38.0611 17.3272 37.6597 17.4412 37.2511 17.4412C36.2835 17.4412 35.581 17.206 35.151 16.7285C34.7209 16.2509 34.5059 15.5311 34.5059 14.5688V9.92883H36.1258V14.412C36.1258 15.0535 36.2476 15.5097 36.4985 15.7734C36.7422 16.0371 37.0934 16.1725 37.5378 16.1725C37.8819 16.1725 38.1614 16.1226 38.3908 16.0157C38.6202 15.9088 38.8065 15.7734 38.9427 15.5952C39.0861 15.4241 39.1864 15.2103 39.2509 14.968C39.3155 14.7256 39.3441 14.4619 39.3441 14.1768V9.93595H40.964V17.2417ZM43.7237 14.8967C43.7738 15.3671 43.953 15.695 44.2612 15.8874C44.5766 16.0727 44.9493 16.1725 45.3866 16.1725C45.5371 16.1725 45.7091 16.1583 45.9027 16.1369C46.0962 16.1155 46.2826 16.0656 46.4474 16.0015C46.6194 15.9373 46.7556 15.8375 46.8703 15.7092C46.9778 15.5809 47.028 15.417 47.0208 15.2103C47.0137 15.0036 46.9348 14.8326 46.7915 14.7043C46.6481 14.5688 46.4689 14.4691 46.2467 14.3835C46.0245 14.3051 45.7736 14.2338 45.4869 14.1768C45.2002 14.1198 44.9135 14.0557 44.6196 13.9915C44.3186 13.9274 44.0247 13.8418 43.7452 13.7492C43.4656 13.6565 43.2147 13.5282 42.9925 13.3643C42.7703 13.2075 42.5911 13.0008 42.4621 12.7513C42.3259 12.5019 42.2614 12.1954 42.2614 11.8247C42.2614 11.4256 42.3618 11.0977 42.5553 10.8269C42.7488 10.556 42.9997 10.3422 43.2936 10.1783C43.5946 10.0144 43.9244 9.90032 44.2899 9.82904C44.6555 9.76489 45.0067 9.72926 45.3364 9.72926C45.7163 9.72926 46.0819 9.77202 46.4259 9.85042C46.77 9.92883 47.0854 10.0571 47.3649 10.2424C47.6444 10.4206 47.8738 10.6558 48.0602 10.9409C48.2465 11.226 48.3612 11.5753 48.4114 11.9815H46.7198C46.6409 11.5967 46.4689 11.3329 46.1894 11.2046C45.9098 11.0692 45.5873 11.0051 45.2289 11.0051C45.1142 11.0051 44.978 11.0122 44.8203 11.0336C44.6626 11.055 44.5193 11.0906 44.3759 11.1405C44.2397 11.1904 44.1251 11.2688 44.0247 11.3686C43.9315 11.4684 43.8813 11.5967 43.8813 11.7606C43.8813 11.9602 43.953 12.117 44.0892 12.2381C44.2254 12.3593 44.4046 12.4591 44.6268 12.5446C44.849 12.623 45.0999 12.6943 45.3866 12.7513C45.6733 12.8083 45.9672 12.8725 46.2682 12.9366C46.5621 13.0008 46.8488 13.0863 47.1355 13.179C47.4222 13.2716 47.6731 13.3999 47.8953 13.5639C48.1175 13.7278 48.2967 13.9274 48.4329 14.1697C48.5691 14.412 48.6408 14.7185 48.6408 15.0749C48.6408 15.5097 48.5404 15.8732 48.3397 16.1797C48.139 16.479 47.881 16.7285 47.5656 16.9138C47.2502 17.0991 46.8918 17.2417 46.5048 17.3272C46.1177 17.4127 45.7306 17.4555 45.3507 17.4555C44.8848 17.4555 44.4548 17.4056 44.0605 17.2987C43.6663 17.1918 43.3223 17.035 43.0355 16.8283C42.7488 16.6144 42.5195 16.3507 42.3546 16.0371C42.1897 15.7235 42.1037 15.3457 42.0894 14.911H43.7237V14.8967ZM49.0708 9.92883H50.2965V7.73355H51.9165V9.92883H53.3787V11.1334H51.9165V15.0393C51.9165 15.2103 51.9236 15.3529 51.938 15.4812C51.9523 15.6023 51.9881 15.7092 52.0383 15.7948C52.0885 15.8803 52.1673 15.9444 52.2748 15.9872C52.3824 16.03 52.5186 16.0514 52.7049 16.0514C52.8196 16.0514 52.9343 16.0514 53.049 16.0442C53.1637 16.0371 53.2783 16.0228 53.393 15.9943V17.2417C53.2138 17.263 53.0346 17.2773 52.8698 17.2987C52.6977 17.3201 52.5257 17.3272 52.3465 17.3272C51.9165 17.3272 51.5724 17.2844 51.3144 17.206C51.0563 17.1276 50.8485 17.0064 50.7051 16.8496C50.5546 16.6928 50.4614 16.5004 50.404 16.2652C50.3539 16.03 50.318 15.7591 50.3109 15.4598V11.1476H49.0852V9.92883H49.0708ZM54.5255 9.92883H56.0594V10.9195H56.0881C56.3175 10.4919 56.6329 10.1925 57.0414 10.0072C57.45 9.82191 57.8872 9.72925 58.3675 9.72925C58.9481 9.72925 59.4498 9.82904 59.8799 10.0357C60.31 10.2353 60.6684 10.5133 60.9551 10.8697C61.2418 11.226 61.4496 11.6394 61.593 12.1098C61.7364 12.5803 61.808 13.0863 61.808 13.6209C61.808 14.1127 61.7435 14.5902 61.6145 15.0464C61.4855 15.5097 61.2919 15.9159 61.0339 16.2723C60.7759 16.6287 60.4461 16.9067 60.0448 17.1205C59.6434 17.3343 59.1774 17.4412 58.6327 17.4412C58.3962 17.4412 58.1596 17.4198 57.9231 17.3771C57.6865 17.3343 57.4572 17.263 57.2421 17.1704C57.0271 17.0777 56.8192 16.9566 56.64 16.8069C56.4537 16.6572 56.3032 16.4861 56.1741 16.2937H56.1455V19.943H54.5255V9.92883ZM60.1881 13.5924C60.1881 13.2645 60.1451 12.9438 60.0591 12.6302C59.9731 12.3165 59.8441 12.0457 59.672 11.8034C59.5 11.561 59.285 11.3686 59.0341 11.226C58.776 11.0835 58.4822 11.0051 58.1525 11.0051C57.4715 11.0051 56.9554 11.2403 56.6114 11.7107C56.2673 12.1811 56.0953 12.8083 56.0953 13.5924C56.0953 13.963 56.1383 14.3051 56.2315 14.6187C56.3247 14.9323 56.4537 15.2032 56.64 15.4313C56.8192 15.6593 57.0343 15.8375 57.2851 15.9658C57.536 16.1013 57.8299 16.1654 58.1596 16.1654C58.5323 16.1654 58.8406 16.087 59.0986 15.9373C59.3566 15.7876 59.5645 15.5881 59.7294 15.3529C59.8942 15.1105 60.0161 14.8397 60.0878 14.5332C60.1523 14.2267 60.1881 13.9131 60.1881 13.5924ZM63.0481 7.14197H64.668V8.66726H63.0481V7.14197ZM63.0481 9.92883H64.668V17.2417H63.0481V9.92883ZM66.1159 7.14197H67.7358V17.2417H66.1159V7.14197ZM72.7031 17.4412C72.1153 17.4412 71.5921 17.3414 71.1334 17.149C70.6746 16.9566 70.2876 16.6857 69.965 16.3507C69.6496 16.0086 69.4059 15.6023 69.241 15.1319C69.0762 14.6615 68.9902 14.1412 68.9902 13.5781C68.9902 13.0222 69.0762 12.509 69.241 12.0386C69.4059 11.5682 69.6496 11.1619 69.965 10.8198C70.2804 10.4776 70.6746 10.2139 71.1334 10.0215C71.5921 9.82904 72.1153 9.72926 72.7031 9.72926C73.2909 9.72926 73.8141 9.82904 74.2729 10.0215C74.7316 10.2139 75.1186 10.4848 75.4412 10.8198C75.7566 11.1619 76.0003 11.5682 76.1651 12.0386C76.33 12.509 76.416 13.0222 76.416 13.5781C76.416 14.1412 76.33 14.6615 76.1651 15.1319C76.0003 15.6023 75.7566 16.0086 75.4412 16.3507C75.1258 16.6928 74.7316 16.9566 74.2729 17.149C73.8141 17.3414 73.2909 17.4412 72.7031 17.4412ZM72.7031 16.1654C73.0615 16.1654 73.3769 16.087 73.6421 15.9373C73.9073 15.7876 74.1223 15.5881 74.2943 15.3457C74.4664 15.1034 74.5882 14.8254 74.6742 14.5189C74.7531 14.2125 74.7961 13.8989 74.7961 13.5781C74.7961 13.2645 74.7531 12.958 74.6742 12.6444C74.5954 12.3308 74.4664 12.06 74.2943 11.8176C74.1223 11.5753 73.9073 11.3828 73.6421 11.2332C73.3769 11.0835 73.0615 11.0051 72.7031 11.0051C72.3447 11.0051 72.0293 11.0835 71.7641 11.2332C71.4989 11.3828 71.2839 11.5824 71.1118 11.8176C70.9398 12.06 70.818 12.3308 70.7319 12.6444C70.6531 12.958 70.6101 13.2645 70.6101 13.5781C70.6101 13.8989 70.6531 14.2125 70.7319 14.5189C70.8108 14.8254 70.9398 15.1034 71.1118 15.3457C71.2839 15.5881 71.4989 15.7876 71.7641 15.9373C72.0293 16.0941 72.3447 16.1654 72.7031 16.1654ZM76.8891 9.92883H78.1148V7.73355H79.7347V9.92883H81.1969V11.1334H79.7347V15.0393C79.7347 15.2103 79.7419 15.3529 79.7562 15.4812C79.7706 15.6023 79.8064 15.7092 79.8566 15.7948C79.9067 15.8803 79.9856 15.9444 80.0931 15.9872C80.2006 16.03 80.3368 16.0514 80.5232 16.0514C80.6379 16.0514 80.7525 16.0514 80.8672 16.0442C80.9819 16.0371 81.0966 16.0228 81.2113 15.9943V17.2417C81.0321 17.263 80.8529 17.2773 80.688 17.2987C80.516 17.3201 80.344 17.3272 80.1648 17.3272C79.7347 17.3272 79.3907 17.2844 79.1326 17.206C78.8746 17.1276 78.6667 17.0064 78.5234 16.8496C78.3728 16.6928 78.2797 16.5004 78.2223 16.2652C78.1721 16.03 78.1363 15.7591 78.1291 15.4598V11.1476H76.9034V9.92883L76.8891 9.92883Z"
                                                   fill="white" />
                                                <path
                                                   d="M19.4463 7.14199H12.0204L9.72672 0.114258L7.42585 7.14198L0 7.13486L6.01379 11.4826L3.71292 18.5032L9.72672 14.1626L15.7333 18.5032L13.4396 11.4826L19.4463 7.14199Z"
                                                   fill="#00B67A" />
                                                <path d="M13.9557 13.072L13.4396 11.4826L9.72668 14.1625L13.9557 13.072Z" fill="#005128" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_2350_3858">
                                                   <rect width="81.2903" height="20" fill="white" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                    </span>
                                 </div>
                              </div>
                           </div>
                           <div class="col-xl-6 col-xxl-5 offset-xxl-1 col-md-6">
                              <div class="text-center mt-lg-8 d-flex justify-content-center">
                                 <img src="../assets/images/mobile-app/mobile-hero-img-light.png" alt="hero img" class="img-fluid dark-mode-none" />
                                 <img src="../assets/images/mobile-app/mobile-hero-img-dark.png" alt="hero img" class="img-fluid d-none dark-mode-block" />
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Hero section-->

         <section class="py-lg-8 py-5 mt-lg-7">
            <div class="container">
               <div class="row">
                  <div class="col-12">
                     <pre class="language-markup" tabindex="0"><code class="language-markup">       <span class="token comment">&lt;!--Hero section--&gt;</span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>section</span>
                         <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>pt-lg-10<span class="token punctuation">"</span></span>
                         <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>
                            background: url(../assets/images/mobile-app/curvlines.svg), linear-gradient(111.42deg, #4a00b7 0.42%, #c13dff 81.76%, #c13dff 96.06%);
                            background-position: center;
                            background-repeat: no-repeat;
                            background-size: cover;
                         <span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>container<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>pt-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-12<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row align-items-center gy-5 px-lg-6 position-relative<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-md-6 col-12<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-column gap-7<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-column gap-3 mb-lg-10<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-row align-items-center<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#!<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bg-opacity-50 text-bg-primary border border-primary badge px-3 py-2 fw-medium rounded-pill fs-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Read our $6M Series A announcement<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>ms-1<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                                                                <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>path</span><span class="token punctuation">&gt;</span></span>
                                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-column gap-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-column gap-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 text-white-stable display-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Master your money with confidence<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 text-white-50 lead<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Navigate your finances with confidence. Track spending, budgets, investments, net worth.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-row align-items-center gap-2<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#!<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/playstore.svg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>playstore<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#!<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/appstore.svg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>appstore<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/qr-code.svg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>qr-code<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>icon-md icon-shape<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-row flex-wrap gap-2 align-items-center justify-content-center justify-content-md-start lh-1 position-lg-absolute bottom-0 mb-lg-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-white-stable fs-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Great<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/star.svg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>star<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/star.svg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>star<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/star.svg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>star<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/star.svg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>star<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/star-light.svg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>star<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-white-stable<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>6,427<span class="token entity named-entity" title="&nbsp;">&amp;nbsp;</span>reviews on<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>lh-1<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>82<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>20<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 82 20<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>g</span> <span class="token attr-name">clip-path</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>url(#clip0_2350_3858)<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M21.3386 7.14197H29.5601V8.66726H26.3274V17.2417H24.5498V8.66726H21.3314V7.14197H21.3386ZM29.2088 9.92883H30.7284V11.3401H30.7571C30.8073 11.1405 30.9004 10.9481 31.0366 10.7627C31.1728 10.5774 31.3377 10.3992 31.5312 10.2496C31.7247 10.0928 31.9398 9.97159 32.1763 9.87181C32.4129 9.77915 32.6566 9.72926 32.9003 9.72926C33.0866 9.72926 33.2228 9.73638 33.2945 9.74351C33.3662 9.75064 33.4378 9.76489 33.5167 9.77202V11.3258C33.402 11.3044 33.2873 11.2902 33.1655 11.2759C33.0436 11.2617 32.9289 11.2545 32.8142 11.2545C32.5419 11.2545 32.2838 11.3116 32.0401 11.4185C31.7964 11.5254 31.5886 11.6893 31.4094 11.896C31.2302 12.1098 31.0868 12.3664 30.9793 12.68C30.8718 12.9937 30.8216 13.35 30.8216 13.7563V17.2345H29.2017V9.92883L29.2088 9.92883ZM40.964 17.2417H39.3728V16.2224H39.3441C39.1434 16.5931 38.8495 16.8853 38.4553 17.1062C38.0611 17.3272 37.6597 17.4412 37.2511 17.4412C36.2835 17.4412 35.581 17.206 35.151 16.7285C34.7209 16.2509 34.5059 15.5311 34.5059 14.5688V9.92883H36.1258V14.412C36.1258 15.0535 36.2476 15.5097 36.4985 15.7734C36.7422 16.0371 37.0934 16.1725 37.5378 16.1725C37.8819 16.1725 38.1614 16.1226 38.3908 16.0157C38.6202 15.9088 38.8065 15.7734 38.9427 15.5952C39.0861 15.4241 39.1864 15.2103 39.2509 14.968C39.3155 14.7256 39.3441 14.4619 39.3441 14.1768V9.93595H40.964V17.2417ZM43.7237 14.8967C43.7738 15.3671 43.953 15.695 44.2612 15.8874C44.5766 16.0727 44.9493 16.1725 45.3866 16.1725C45.5371 16.1725 45.7091 16.1583 45.9027 16.1369C46.0962 16.1155 46.2826 16.0656 46.4474 16.0015C46.6194 15.9373 46.7556 15.8375 46.8703 15.7092C46.9778 15.5809 47.028 15.417 47.0208 15.2103C47.0137 15.0036 46.9348 14.8326 46.7915 14.7043C46.6481 14.5688 46.4689 14.4691 46.2467 14.3835C46.0245 14.3051 45.7736 14.2338 45.4869 14.1768C45.2002 14.1198 44.9135 14.0557 44.6196 13.9915C44.3186 13.9274 44.0247 13.8418 43.7452 13.7492C43.4656 13.6565 43.2147 13.5282 42.9925 13.3643C42.7703 13.2075 42.5911 13.0008 42.4621 12.7513C42.3259 12.5019 42.2614 12.1954 42.2614 11.8247C42.2614 11.4256 42.3618 11.0977 42.5553 10.8269C42.7488 10.556 42.9997 10.3422 43.2936 10.1783C43.5946 10.0144 43.9244 9.90032 44.2899 9.82904C44.6555 9.76489 45.0067 9.72926 45.3364 9.72926C45.7163 9.72926 46.0819 9.77202 46.4259 9.85042C46.77 9.92883 47.0854 10.0571 47.3649 10.2424C47.6444 10.4206 47.8738 10.6558 48.0602 10.9409C48.2465 11.226 48.3612 11.5753 48.4114 11.9815H46.7198C46.6409 11.5967 46.4689 11.3329 46.1894 11.2046C45.9098 11.0692 45.5873 11.0051 45.2289 11.0051C45.1142 11.0051 44.978 11.0122 44.8203 11.0336C44.6626 11.055 44.5193 11.0906 44.3759 11.1405C44.2397 11.1904 44.1251 11.2688 44.0247 11.3686C43.9315 11.4684 43.8813 11.5967 43.8813 11.7606C43.8813 11.9602 43.953 12.117 44.0892 12.2381C44.2254 12.3593 44.4046 12.4591 44.6268 12.5446C44.849 12.623 45.0999 12.6943 45.3866 12.7513C45.6733 12.8083 45.9672 12.8725 46.2682 12.9366C46.5621 13.0008 46.8488 13.0863 47.1355 13.179C47.4222 13.2716 47.6731 13.3999 47.8953 13.5639C48.1175 13.7278 48.2967 13.9274 48.4329 14.1697C48.5691 14.412 48.6408 14.7185 48.6408 15.0749C48.6408 15.5097 48.5404 15.8732 48.3397 16.1797C48.139 16.479 47.881 16.7285 47.5656 16.9138C47.2502 17.0991 46.8918 17.2417 46.5048 17.3272C46.1177 17.4127 45.7306 17.4555 45.3507 17.4555C44.8848 17.4555 44.4548 17.4056 44.0605 17.2987C43.6663 17.1918 43.3223 17.035 43.0355 16.8283C42.7488 16.6144 42.5195 16.3507 42.3546 16.0371C42.1897 15.7235 42.1037 15.3457 42.0894 14.911H43.7237V14.8967ZM49.0708 9.92883H50.2965V7.73355H51.9165V9.92883H53.3787V11.1334H51.9165V15.0393C51.9165 15.2103 51.9236 15.3529 51.938 15.4812C51.9523 15.6023 51.9881 15.7092 52.0383 15.7948C52.0885 15.8803 52.1673 15.9444 52.2748 15.9872C52.3824 16.03 52.5186 16.0514 52.7049 16.0514C52.8196 16.0514 52.9343 16.0514 53.049 16.0442C53.1637 16.0371 53.2783 16.0228 53.393 15.9943V17.2417C53.2138 17.263 53.0346 17.2773 52.8698 17.2987C52.6977 17.3201 52.5257 17.3272 52.3465 17.3272C51.9165 17.3272 51.5724 17.2844 51.3144 17.206C51.0563 17.1276 50.8485 17.0064 50.7051 16.8496C50.5546 16.6928 50.4614 16.5004 50.404 16.2652C50.3539 16.03 50.318 15.7591 50.3109 15.4598V11.1476H49.0852V9.92883H49.0708ZM54.5255 9.92883H56.0594V10.9195H56.0881C56.3175 10.4919 56.6329 10.1925 57.0414 10.0072C57.45 9.82191 57.8872 9.72925 58.3675 9.72925C58.9481 9.72925 59.4498 9.82904 59.8799 10.0357C60.31 10.2353 60.6684 10.5133 60.9551 10.8697C61.2418 11.226 61.4496 11.6394 61.593 12.1098C61.7364 12.5803 61.808 13.0863 61.808 13.6209C61.808 14.1127 61.7435 14.5902 61.6145 15.0464C61.4855 15.5097 61.2919 15.9159 61.0339 16.2723C60.7759 16.6287 60.4461 16.9067 60.0448 17.1205C59.6434 17.3343 59.1774 17.4412 58.6327 17.4412C58.3962 17.4412 58.1596 17.4198 57.9231 17.3771C57.6865 17.3343 57.4572 17.263 57.2421 17.1704C57.0271 17.0777 56.8192 16.9566 56.64 16.8069C56.4537 16.6572 56.3032 16.4861 56.1741 16.2937H56.1455V19.943H54.5255V9.92883ZM60.1881 13.5924C60.1881 13.2645 60.1451 12.9438 60.0591 12.6302C59.9731 12.3165 59.8441 12.0457 59.672 11.8034C59.5 11.561 59.285 11.3686 59.0341 11.226C58.776 11.0835 58.4822 11.0051 58.1525 11.0051C57.4715 11.0051 56.9554 11.2403 56.6114 11.7107C56.2673 12.1811 56.0953 12.8083 56.0953 13.5924C56.0953 13.963 56.1383 14.3051 56.2315 14.6187C56.3247 14.9323 56.4537 15.2032 56.64 15.4313C56.8192 15.6593 57.0343 15.8375 57.2851 15.9658C57.536 16.1013 57.8299 16.1654 58.1596 16.1654C58.5323 16.1654 58.8406 16.087 59.0986 15.9373C59.3566 15.7876 59.5645 15.5881 59.7294 15.3529C59.8942 15.1105 60.0161 14.8397 60.0878 14.5332C60.1523 14.2267 60.1881 13.9131 60.1881 13.5924ZM63.0481 7.14197H64.668V8.66726H63.0481V7.14197ZM63.0481 9.92883H64.668V17.2417H63.0481V9.92883ZM66.1159 7.14197H67.7358V17.2417H66.1159V7.14197ZM72.7031 17.4412C72.1153 17.4412 71.5921 17.3414 71.1334 17.149C70.6746 16.9566 70.2876 16.6857 69.965 16.3507C69.6496 16.0086 69.4059 15.6023 69.241 15.1319C69.0762 14.6615 68.9902 14.1412 68.9902 13.5781C68.9902 13.0222 69.0762 12.509 69.241 12.0386C69.4059 11.5682 69.6496 11.1619 69.965 10.8198C70.2804 10.4776 70.6746 10.2139 71.1334 10.0215C71.5921 9.82904 72.1153 9.72926 72.7031 9.72926C73.2909 9.72926 73.8141 9.82904 74.2729 10.0215C74.7316 10.2139 75.1186 10.4848 75.4412 10.8198C75.7566 11.1619 76.0003 11.5682 76.1651 12.0386C76.33 12.509 76.416 13.0222 76.416 13.5781C76.416 14.1412 76.33 14.6615 76.1651 15.1319C76.0003 15.6023 75.7566 16.0086 75.4412 16.3507C75.1258 16.6928 74.7316 16.9566 74.2729 17.149C73.8141 17.3414 73.2909 17.4412 72.7031 17.4412ZM72.7031 16.1654C73.0615 16.1654 73.3769 16.087 73.6421 15.9373C73.9073 15.7876 74.1223 15.5881 74.2943 15.3457C74.4664 15.1034 74.5882 14.8254 74.6742 14.5189C74.7531 14.2125 74.7961 13.8989 74.7961 13.5781C74.7961 13.2645 74.7531 12.958 74.6742 12.6444C74.5954 12.3308 74.4664 12.06 74.2943 11.8176C74.1223 11.5753 73.9073 11.3828 73.6421 11.2332C73.3769 11.0835 73.0615 11.0051 72.7031 11.0051C72.3447 11.0051 72.0293 11.0835 71.7641 11.2332C71.4989 11.3828 71.2839 11.5824 71.1118 11.8176C70.9398 12.06 70.818 12.3308 70.7319 12.6444C70.6531 12.958 70.6101 13.2645 70.6101 13.5781C70.6101 13.8989 70.6531 14.2125 70.7319 14.5189C70.8108 14.8254 70.9398 15.1034 71.1118 15.3457C71.2839 15.5881 71.4989 15.7876 71.7641 15.9373C72.0293 16.0941 72.3447 16.1654 72.7031 16.1654ZM76.8891 9.92883H78.1148V7.73355H79.7347V9.92883H81.1969V11.1334H79.7347V15.0393C79.7347 15.2103 79.7419 15.3529 79.7562 15.4812C79.7706 15.6023 79.8064 15.7092 79.8566 15.7948C79.9067 15.8803 79.9856 15.9444 80.0931 15.9872C80.2006 16.03 80.3368 16.0514 80.5232 16.0514C80.6379 16.0514 80.7525 16.0514 80.8672 16.0442C80.9819 16.0371 81.0966 16.0228 81.2113 15.9943V17.2417C81.0321 17.263 80.8529 17.2773 80.688 17.2987C80.516 17.3201 80.344 17.3272 80.1648 17.3272C79.7347 17.3272 79.3907 17.2844 79.1326 17.206C78.8746 17.1276 78.6667 17.0064 78.5234 16.8496C78.3728 16.6928 78.2797 16.5004 78.2223 16.2652C78.1721 16.03 78.1363 15.7591 78.1291 15.4598V11.1476H76.9034V9.92883L76.8891 9.92883Z<span class="token punctuation">"</span></span>
                                                                <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>white<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M19.4463 7.14199H12.0204L9.72672 0.114258L7.42585 7.14198L0 7.13486L6.01379 11.4826L3.71292 18.5032L9.72672 14.1626L15.7333 18.5032L13.4396 11.4826L19.4463 7.14199Z<span class="token punctuation">"</span></span>
                                                                <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#00B67A<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span> <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M13.9557 13.072L13.4396 11.4826L9.72668 14.1625L13.9557 13.072Z<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#005128<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>g</span><span class="token punctuation">&gt;</span></span>
                                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>defs</span><span class="token punctuation">&gt;</span></span>
                                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>clipPath</span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>clip0_2350_3858<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>rect</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>81.2903<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>20<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>white<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>clipPath</span><span class="token punctuation">&gt;</span></span>
                                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>defs</span><span class="token punctuation">&gt;</span></span>
                                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-xl-6 col-xxl-5 offset-xxl-1 col-md-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-center mt-lg-8 d-flex justify-content-center<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/mobile-hero-img-light.png<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>hero img<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>img-fluid dark-mode-none<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/mobile-app/mobile-hero-img-dark.png<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>hero img<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>img-fluid d-none dark-mode-block<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>section</span><span class="token punctuation">&gt;</span></span>
                      <span class="token comment">&lt;!--Hero section--&gt;</span></code></pre>
                  </div>
               </div>
            </div>
         </section>
      </main>
      <!-- Modal -->
      <div class="modal fade" id="bookConsultantModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
         <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
               <div class="container p-0">
                  <div class="row g-0">
                     <div class="col-lg-6 d-none d-lg-block">
                        <div class="bg-light border-end p-6 rounded-start-3 d-flex flex-column gap-6 object-fit-cover" style="min-height: 100%">
                           <div>
                              <ul class="list-unstyled d-flex flex-column gap-2">
                                 <li class="d-flex flex-row gap-2">
                                    <span>
                                       <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                          <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                       </svg>
                                    </span>
                                    <span>Rankings Improvement</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2">
                                    <span>
                                       <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                          <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                       </svg>
                                    </span>
                                    <span>Boost organic traffic</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2">
                                    <span>
                                       <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                          <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                       </svg>
                                    </span>
                                    <span>Conversion Rate Enhancement</span>
                                 </li>
                              </ul>
                           </div>
                           <div class="my-auto">
                              <p>
                                 “Working with [Agency Name] has been an absolute game-changer for our online presence. Their team took the time to understand our unique needs and crafted a tailored
                                 SEO strategy that has delivered incredible results.”
                              </p>
                              <div>
                                 <span class="text-dark fw-semibold">John D.</span>
                                 <p class="mb-0 fs-6">CEO of [Client Company]</p>
                                 <div>
                                    <img src="./assets/images/brand-logo/brand-logo-13.svg" alt="" />
                                 </div>
                              </div>
                           </div>
                           <div>
                              <span>Do you have a question?</span>
                              <h3 class="mt-2 mb-0">123-456-7890</h3>
                              <span>8 AM - 8 PM IST</span>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-6">
                        <div class="pt-4 px-4">
                           <div class="text-end">
                              <button type="button" class="btn-close small" data-bs-dismiss="modal" aria-label="Close"></button>
                           </div>
                        </div>
                        <div class="pb-4 px-6">
                           <h3 class="mb-4">Get Started Right Now!</h3>
                           <form class="needs-validation mb-3" novalidate="">
                              <div class="mb-3">
                                 <label for="FullnameInput" class="form-label visually-hidden">Full Name</label>
                                 <input type="text" class="form-control" id="FullnameInput" required="" placeholder="Name" />
                                 <div class="invalid-feedback">Please enter full name</div>
                              </div>

                              <div class="mb-3">
                                 <label for="EmailInput" class="form-label visually-hidden">Email</label>
                                 <input type="email" class="form-control" id="EmailInput" required="" placeholder="Email" />
                                 <div class="invalid-feedback">Please enter email.</div>
                              </div>
                              <div class="mb-3">
                                 <label for="webURL" class="form-label visually-hidden">Website</label>
                                 <input type="text" class="form-control" id="webURL" required="" placeholder="Website URL" />
                                 <div class="invalid-feedback">Please enter URL.</div>
                              </div>
                              <div class="mb-3">
                                 <label for="phone" class="form-label visually-hidden">Phone</label>
                                 <input type="tel" name="phone" placeholder="" class="form-control w-100" required autocomplete="phone" id="phone" />

                                 <div class="invalid-feedback">Please enter Phone.</div>
                              </div>

                              <div class="mb-3">
                                 <label for="messages" class="form-label visually-hidden">Messages</label>
                                 <textarea rows="3" class="form-control" id="messages" placeholder="Messages"></textarea>
                              </div>

                              <div class="">
                                 <button class="btn btn-primary" type="submit">Get Your Free SEO Consultation</button>
                              </div>
                           </form>
                           <div class="">
                              <small>
                                 By submitting this form, you are agree to the terms outlined in our 
                                 <a href="#" class="link-primary">privacy policy</a>
                                 .
                              </small>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
      @@include("../partials/footer.html") @@include("../partials/register-offcanvas.html") @@include("../partials/btn-scroll-top.html") @@include("../partials/scripts.html")
      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>

      <script src="@@webRoot/node_modules/scrollcue/scrollCue.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/scrollcue.js"></script>
      <script src="@@webRoot/node_modules/intl-tel-input/build/js/intlTelInput.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/intl-tel.js"></script>
   </body>
</html>
