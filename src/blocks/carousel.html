<!doctype html>
<html lang="en">
   <head>
      @@include("../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      <link rel="stylesheet" href="@@webRoot/node_modules/swiper/swiper-bundle.min.css" />
      @@include("../partials/head/head-links.html")
      <title>Carousel Snippet- Responsive Website Template | Block</title>
   </head>

   <body>
      @@include("../partials/navbar.html",{ "classList": " navbar-light w-100" })
      <main>
         <div class="pattern-square"></div>
         <section class="bg-light py-5 py-lg-8 bg-opacity-50">
            <div class="container">
               <div class="row">
                  <div class="col-12 col-md-6">
                     <div>
                        <h1 class="mb-0">Carousel Sections</h1>
                     </div>
                  </div>
                  @@include("../partials/filter-tabs.html", { "page": "carousel" })
               </div>
            </div>
         </section>

         <section class="py-lg-8 py-5">
            <div class="container">
               <div class="mb-lg-7 mb-5">
                  <div class="row align-items-center">
                     <div class="col-lg-8 col-xl-9 col-7">
                        <div>
                           <h2 class="text-truncate h5 mb-0">Carousel #1</h2>
                        </div>
                     </div>
                     <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                        <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                           <li class="nav-item">
                              <a
                                 class="nav-link active"
                                 id="pills-event-carousel-preview-tab"
                                 data-bs-toggle="pill"
                                 href="#pills-event-carousel-preview"
                                 role="tab"
                                 aria-controls="pills-event-carousel-preview"
                                 aria-selected="true">
                                 <span class="lh-1"><i class="bi bi-eye"></i></span>
                                 <span class="ms-2 d-none d-lg-block">Preview</span>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a
                                 class="nav-link"
                                 id="pills-event-carousel-code-tab"
                                 data-bs-toggle="pill"
                                 href="#pills-event-carousel-code"
                                 role="tab"
                                 aria-controls="pills-event-carousel-code"
                                 aria-selected="false">
                                 <span class="lh-1"><i class="bi bi-code"></i></span>
                                 <span class="ms-2 d-none d-lg-block">Code</span>
                              </a>
                           </li>
                        </ul>
                     </div>
                  </div>
                  <div class="row">
                     <div class="col-md-12">
                        <div class="tab-content border mt-3 p-3 rounded-2" id="pills-tabTwoContent">
                           <div class="tab-pane tab-example-preview fade show active" id="pills-event-carousel-preview" role="tabpanel" aria-labelledby="pills-event-carousel-preview-tab">
                              <!--online-->

                              <section>
                                 <div class="container">
                                    <div class="row">
                                       <div class="col-lg-12">
                                          <div
                                             class="swiper-container swiper"
                                             id="swiper-1"
                                             data-pagination-type=""
                                             data-speed="400"
                                             data-space-between="100"
                                             data-pagination="true"
                                             data-navigation="false"
                                             data-autoplay="true"
                                             data-autoplay-delay="3000"
                                             data-breakpoints='{"480": {"slidesPerView": 1}, "768": {"slidesPerView": 1}, "1024": {"slidesPerView": 1}}'>
                                             <div class="swiper-wrapper pb-6 mx-auto">
                                                <div class="swiper-slide">
                                                   <div class="card shadow-sm overflow-hidden">
                                                      <div class="row g-0">
                                                         <div class="col-xl-6 col-md-6">
                                                            <div class="card-body h-100 d-flex align-items-start flex-column p-lg-7">
                                                               <div class="mb-3">
                                                                  <small class="text-uppercase fw-semibold ls-md">Online</small>
                                                                  <h2 class="mb-0 mt-3">
                                                                     <a href="#" class="text-reset">React - Next.js developers events and meetup</a>
                                                                  </h2>
                                                               </div>
                                                               <div class="mb-5">
                                                                  <small class="me-2">June 22, 2024</small>
                                                                  <small>1:00PM EDT</small>
                                                               </div>
                                                               <div class="mt-auto">
                                                                  <a href="./event-single.html" class="icon-link icon-link-hover card-link">
                                                                     Mode Details
                                                                     <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                                        <path
                                                                           fill-rule="evenodd"
                                                                           d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                                                     </svg>
                                                                  </a>
                                                               </div>
                                                            </div>
                                                         </div>
                                                         <div
                                                            class="col-md-6"
                                                            style="
                                                               background-image: url(../assets/images/event/event-img-1.jpg);
                                                               background-size: cover;
                                                               background-repeat: no-repeat;
                                                               background-position: center;
                                                               min-height: 15rem;
                                                            ">
                                                            <!-- for mobile img-->
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                                <div class="swiper-slide">
                                                   <div class="card shadow-sm overflow-hidden">
                                                      <div class="row g-0">
                                                         <div class="col-xl-6 col-md-6">
                                                            <div class="card-body h-100 d-flex align-items-start flex-column p-lg-7">
                                                               <div class="mb-4">
                                                                  <small class="text-uppercase fw-semibold ls-md">Online</small>
                                                                  <h2 class="mb-0 mt-3">
                                                                     <a href="#" class="text-reset">React - Next.js developers events and meetup</a>
                                                                  </h2>
                                                               </div>
                                                               <div class="mb-5">
                                                                  <small class="me-2">June 22, 2024</small>
                                                                  <small>1:00PM EDT</small>
                                                               </div>
                                                               <div class="mt-auto">
                                                                  <a href="./event-single.html" class="icon-link icon-link-hover card-link">
                                                                     Mode Details
                                                                     <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                                        <path
                                                                           fill-rule="evenodd"
                                                                           d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                                                     </svg>
                                                                  </a>
                                                               </div>
                                                            </div>
                                                         </div>
                                                         <div
                                                            class="col-md-6"
                                                            style="
                                                               background-image: url(../assets/images/event/event-img-2.jpg);
                                                               background-size: cover;
                                                               background-repeat: no-repeat;
                                                               background-position: center;
                                                               min-height: 15rem;
                                                            ">
                                                            <!-- for mobile img-->
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                                <div class="swiper-slide">
                                                   <div class="card shadow-sm overflow-hidden">
                                                      <div class="row g-0">
                                                         <div class="col-xl-6 col-md-6">
                                                            <div class="card-body h-100 d-flex align-items-start flex-column p-lg-7">
                                                               <div class="mb-4">
                                                                  <small class="text-uppercase fw-semibold ls-md">Online</small>
                                                                  <h2 class="mb-0 mt-3">
                                                                     <a href="#" class="text-reset">React - Next.js developers events and meetup</a>
                                                                  </h2>
                                                               </div>
                                                               <div class="mb-5">
                                                                  <small class="me-2">June 22, 2024</small>
                                                                  <small>1:00PM EDT</small>
                                                               </div>
                                                               <div class="mt-auto">
                                                                  <a href="./event-single.html" class="icon-link icon-link-hover card-link">
                                                                     Mode Details
                                                                     <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                                        <path
                                                                           fill-rule="evenodd"
                                                                           d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                                                     </svg>
                                                                  </a>
                                                               </div>
                                                            </div>
                                                         </div>
                                                         <div
                                                            class="col-md-6"
                                                            style="
                                                               background-image: url(../assets/images/event/event-img-3.jpg);
                                                               background-size: cover;
                                                               background-repeat: no-repeat;
                                                               background-position: center;
                                                               min-height: 15rem;
                                                            ">
                                                            <!-- for mobile img-->
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                                <!-- Add more slides as needed -->
                                             </div>
                                             <!-- Add Pagination -->
                                             <div class="swiper-pagination"></div>
                                             <!-- Add Navigation -->
                                             <div class="swiper-navigation">
                                                <div class="swiper-button-next"></div>
                                                <div class="swiper-button-prev"></div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </section>
                              <!--online-->
                           </div>
                           <div class="tab-pane tab-example-code fade" id="pills-event-carousel-code" role="tabpanel" aria-labelledby="pills-event-carousel-code-tab">
                              <pre
                                 class="language-markup"
                                 tabindex="0"><code class="language-markup"> <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span>
 <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-container swiper<span class="token punctuation">"</span></span>
 <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-1<span class="token punctuation">"</span></span>
 <span class="token attr-name">data-pagination-type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span>
 <span class="token attr-name">data-speed</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>400<span class="token punctuation">"</span></span>
 <span class="token attr-name">data-space-between</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>100<span class="token punctuation">"</span></span>
 <span class="token attr-name">data-pagination</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span>
 <span class="token attr-name">data-navigation</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>false<span class="token punctuation">"</span></span>
 <span class="token attr-name">data-autoplay</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span>
 <span class="token attr-name">data-autoplay-delay</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3000<span class="token punctuation">"</span></span>
 <span class="token attr-name">data-breakpoints</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">'</span>{"480": {"slidesPerView": 1}, "768": {"slidesPerView": 1}, "1024": {"slidesPerView": 1}}<span class="token punctuation">'</span></span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-wrapper pb-6 mx-auto<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-slide<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card shadow-sm overflow-hidden<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row g-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-xl-6 col-md-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-body h-100 d-flex align-items-start flex-column p-lg-7<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-uppercase fw-semibold ls-md<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Online<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h2</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 mt-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-reset<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>React - Next.js developers events and meetup<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h2</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>me-2<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>June 22, 2024<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span><span class="token punctuation">&gt;</span></span>1:00PM EDT<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mt-auto<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>./event-single.html<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>icon-link icon-link-hover card-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         Mode Details
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span>
                            <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span>
                            <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>14<span class="token punctuation">"</span></span>
                            <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>14<span class="token punctuation">"</span></span>
                            <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span>
                            <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span>
                            <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                               <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                               <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>path</span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span>
                <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-md-6<span class="token punctuation">"</span></span>
                <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>
                   background-image: url(../assets/images/event/event-img-1.jpg);
                   background-size: cover;
                   background-repeat: no-repeat;
                   background-position: center;
                   min-height: 15rem;
                <span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                <span class="token comment">&lt;!-- for mobile img--&gt;</span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-slide<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card shadow-sm overflow-hidden<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row g-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-xl-6 col-md-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-body h-100 d-flex align-items-start flex-column p-lg-7<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-uppercase fw-semibold ls-md<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Online<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h2</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 mt-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-reset<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>React - Next.js developers events and meetup<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h2</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>me-2<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>June 22, 2024<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span><span class="token punctuation">&gt;</span></span>1:00PM EDT<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mt-auto<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>./event-single.html<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>icon-link icon-link-hover card-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         Mode Details
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span>
                            <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span>
                            <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>14<span class="token punctuation">"</span></span>
                            <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>14<span class="token punctuation">"</span></span>
                            <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span>
                            <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span>
                            <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                               <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                               <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>path</span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span>
                <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-md-6<span class="token punctuation">"</span></span>
                <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>
                   background-image: url(../assets/images/event/event-img-2.jpg);
                   background-size: cover;
                   background-repeat: no-repeat;
                   background-position: center;
                   min-height: 15rem;
                <span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                <span class="token comment">&lt;!-- for mobile img--&gt;</span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-slide<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card shadow-sm overflow-hidden<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row g-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-xl-6 col-md-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-body h-100 d-flex align-items-start flex-column p-lg-7<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-uppercase fw-semibold ls-md<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Online<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h2</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 mt-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-reset<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>React - Next.js developers events and meetup<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h2</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>me-2<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>June 22, 2024<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span><span class="token punctuation">&gt;</span></span>1:00PM EDT<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mt-auto<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>./event-single.html<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>icon-link icon-link-hover card-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         Mode Details
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span>
                            <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span>
                            <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>14<span class="token punctuation">"</span></span>
                            <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>14<span class="token punctuation">"</span></span>
                            <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span>
                            <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span>
                            <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                               <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                               <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>path</span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span>
                <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-md-6<span class="token punctuation">"</span></span>
                <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>
                   background-image: url(../assets/images/event/event-img-3.jpg);
                   background-size: cover;
                   background-repeat: no-repeat;
                   background-position: center;
                   min-height: 15rem;
                <span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                <span class="token comment">&lt;!-- for mobile img--&gt;</span>
             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token comment">&lt;!-- Add more slides as needed --&gt;</span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
 <span class="token comment">&lt;!-- Add Pagination --&gt;</span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-pagination<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
 <span class="token comment">&lt;!-- Add Navigation --&gt;</span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-navigation<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-button-next<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-button-prev<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="mb-lg-7 mb-5">
                  <div class="row align-items-center">
                     <div class="col-lg-8 col-xl-9 col-7">
                        <div>
                           <h2 class="text-truncate h5 mb-0">Carousel #2</h2>
                        </div>
                     </div>
                     <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                        <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                           <li class="nav-item">
                              <a
                                 class="nav-link active"
                                 id="pills-event-carousel-one-preview-tab"
                                 data-bs-toggle="pill"
                                 href="#pills-event-carousel-one-preview"
                                 role="tab"
                                 aria-controls="pills-event-carousel-one-preview"
                                 aria-selected="true">
                                 <span class="lh-1"><i class="bi bi-eye"></i></span>
                                 <span class="ms-2 d-none d-lg-block">Preview</span>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a
                                 class="nav-link"
                                 id="pills-event-carousel-one-code-tab"
                                 data-bs-toggle="pill"
                                 href="#pills-event-carousel-one-code"
                                 role="tab"
                                 aria-controls="pills-event-carousel-one-code"
                                 aria-selected="false">
                                 <span class="lh-1"><i class="bi bi-code"></i></span>
                                 <span class="ms-2 d-none d-lg-block">Code</span>
                              </a>
                           </li>
                        </ul>
                     </div>
                  </div>
                  <div class="row">
                     <div class="col-md-12">
                        <div class="tab-content border mt-3 p-3 rounded-2" id="pills-tabTwoContent">
                           <div class="tab-pane tab-example-preview fade show active" id="pills-event-carousel-one-preview" role="tabpanel" aria-labelledby="pills-event-carousel-one-preview-tab">
                              <!--Portfolio project start-->
                              <section>
                                 <div class="container">
                                    <div class="row align-items-center">
                                       <div class="col-lg-6">
                                          <div class="mb-6 mb-lg-0">
                                             <small class="text-primary text-uppercase ls-md fw-bold">portfolio</small>

                                             <div class="my-4">
                                                <h2 class="mb-3">Our favourite portfolio project.</h2>
                                                <p class="lead mb-0">
                                                   We’ll design a beautiful, scalable, and modular website that your design team will drool over and marketing team will thank you for.
                                                </p>
                                             </div>

                                             <ul class="list-unstyled mb-0">
                                                <li class="d-flex mb-2">
                                                   <span>
                                                      <svg
                                                         xmlns="http://www.w3.org/2000/svg"
                                                         width="16"
                                                         height="16"
                                                         fill="currentColor"
                                                         class="bi bi-check-circle-fill text-success text-opacity-50"
                                                         viewBox="0 0 16 16">
                                                         <path
                                                            d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                                      </svg>
                                                   </span>
                                                   <span class="ms-2">Design discovery workshop</span>
                                                </li>
                                                <li class="d-flex mb-2">
                                                   <span>
                                                      <svg
                                                         xmlns="http://www.w3.org/2000/svg"
                                                         width="16"
                                                         height="16"
                                                         fill="currentColor"
                                                         class="bi bi-check-circle-fill text-success text-opacity-50"
                                                         viewBox="0 0 16 16">
                                                         <path
                                                            d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                                      </svg>
                                                   </span>
                                                   <span class="ms-2">Conversion focused design</span>
                                                </li>
                                                <li class="d-flex mb-2">
                                                   <span>
                                                      <svg
                                                         xmlns="http://www.w3.org/2000/svg"
                                                         width="16"
                                                         height="16"
                                                         fill="currentColor"
                                                         class="bi bi-check-circle-fill text-success text-opacity-50"
                                                         viewBox="0 0 16 16">
                                                         <path
                                                            d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                                      </svg>
                                                   </span>
                                                   <span class="ms-2">Responsive across major breakpoints</span>
                                                </li>
                                                <li class="d-flex mb-2">
                                                   <span>
                                                      <svg
                                                         xmlns="http://www.w3.org/2000/svg"
                                                         width="16"
                                                         height="16"
                                                         fill="currentColor"
                                                         class="bi bi-check-circle-fill text-success text-opacity-50"
                                                         viewBox="0 0 16 16">
                                                         <path
                                                            d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                                      </svg>
                                                   </span>
                                                   <span class="ms-2">Comprehensive design documentation</span>
                                                </li>
                                             </ul>
                                          </div>
                                       </div>
                                       <div class="col-lg-5 offset-lg-1">
                                          <div
                                             class="swiper-container swiper"
                                             id="swiper-1"
                                             data-pagination-type=""
                                             data-speed="400"
                                             data-space-between="100"
                                             data-pagination="true"
                                             data-navigation="false"
                                             data-autoplay="true"
                                             data-autoplay-delay="3000"
                                             data-breakpoints='{"480": {"slidesPerView": 1}, "768": {"slidesPerView": 1}, "1024": {"slidesPerView": 1}}'>
                                             <div class="swiper-wrapper pb-6 mx-auto">
                                                <div class="swiper-slide">
                                                   <img src="../assets/images/service/project-small-img-1.jpg" alt="project" class="img-fluid rounded-3" />
                                                </div>
                                                <div class="swiper-slide">
                                                   <img src="../assets/images/service/project-small-img-2.jpg" alt="project" class="img-fluid rounded-3" />
                                                </div>
                                                <div class="swiper-slide">
                                                   <img src="../assets/images/service/project-small-img-3.jpg" alt="project" class="img-fluid rounded-3" />
                                                </div>
                                                <!-- Add more slides as needed -->
                                             </div>
                                             <!-- Add Pagination -->
                                             <div class="swiper-pagination"></div>
                                             <!-- Add Navigation -->
                                             <div class="swiper-navigation">
                                                <div class="swiper-button-next"></div>
                                                <div class="swiper-button-prev"></div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </section>
                              <!--Portfolio project end-->
                           </div>
                           <div class="tab-pane tab-example-code fade" id="pills-event-carousel-one-code" role="tabpanel" aria-labelledby="pills-event-carousel-one-code-tab">
                              <pre
                                 class="language-markup"
                                 tabindex="0"><code class="language-markup">  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-container swiper<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-1<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-pagination-type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-speed</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>400<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-space-between</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>100<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-pagination</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-navigation</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>false<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-autoplay</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-autoplay-delay</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3000<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-breakpoints</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">'</span>{"480": {"slidesPerView": 1}, "768": {"slidesPerView": 1}, "1024": {"slidesPerView": 1}}<span class="token punctuation">'</span></span><span class="token punctuation">&gt;</span></span>
                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-wrapper pb-6 mx-auto<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-slide<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/service/project-small-img-1.jpg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>project<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>img-fluid rounded-3<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-slide<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/service/project-small-img-2.jpg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>project<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>img-fluid rounded-3<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-slide<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/service/project-small-img-3.jpg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>project<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>img-fluid rounded-3<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                          <span class="token comment">&lt;!-- Add more slides as needed --&gt;</span>
                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                       <span class="token comment">&lt;!-- Add Pagination --&gt;</span>
                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-pagination<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                       <span class="token comment">&lt;!-- Add Navigation --&gt;</span>
                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-navigation<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-button-next<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>swiper-button-prev<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
      </main>
      @@include("../partials/footer.html") @@include("../partials/btn-scroll-top.html") @@include("../partials/scripts.html")
      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>

      <!-- Swiper JS -->
      <script src="@@webRoot/node_modules/swiper/swiper-bundle.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/swiper.js"></script>
   </body>
</html>
