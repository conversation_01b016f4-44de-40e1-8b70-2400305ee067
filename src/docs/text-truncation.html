<!doctype html>
<html lang="en">
   <head>
      @@include("../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      @@include("../partials/head/head-links.html")

      <title>Text Truncation - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Main wrapper -->
      <main class="docs-main-wrapper">
         @@include("../partials/docs-navbar.html")
         <!-- left sidebar -->
         @@include("../partials/docs-sidenav.html")
         <!-- Wrapper  -->
         <div class="docs-wrapper">
            <div class="docs-content">
               <!-- Content -->
               <div class="container">
                  <div class="row">
                     <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        <div class="mb-5" id="intro">
                           <h1>Text truncation</h1>
                           <p class="mb-0">Truncate long strings of text with an ellipsis.</p>
                        </div>
                     </div>
                  </div>
                  <!--Text truncation-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h2 class="text-truncate h5 mb-0">Text truncation</h2>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-text-truncation-preview-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-text-truncation-preview"
                                    role="tab"
                                    aria-controls="pills-text-truncation-preview"
                                    aria-selected="true">
                                    <span class="lh-1"><i class="bi bi-eye"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Preview</span>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a
                                    class="nav-link"
                                    id="pills-text-truncation-code-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-text-truncation-code"
                                    role="tab"
                                    aria-controls="pills-text-truncation-code"
                                    aria-selected="false">
                                    <span class="lh-1"><i class="bi bi-code"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Code</span>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-text-truncation-preview" role="tabpanel" aria-labelledby="pills-text-truncation-preview-tab">
                                 <!--Text truncation-->
                                 <!-- Block level -->
                                 <div class="row">
                                    <div class="col-2 text-truncate">This text is quite long, and will be truncated once displayed.</div>
                                 </div>

                                 <!-- Inline level -->
                                 <span class="d-inline-block text-truncate" style="max-width: 150px">This text is quite long, and will be truncated once displayed.</span>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-text-truncation-code" role="tabpanel" aria-labelledby="pills-text-truncation-code-tab">
                                 <pre class="language-markup" tabindex="0"><code class="language-markup"><span class="token comment">&lt;!--Text truncation--&gt;</span>
<span class="token comment">&lt;!-- Block level --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-2 text-truncate<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        This text is quite long, and will be truncated once displayed.
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>

<span class="token comment">&lt;!-- Inline level --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-inline-block text-truncate<span class="token punctuation">"</span></span> <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>max-width: 150px;<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    This text is quite long, and will be truncated once displayed.
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Text truncation-->
               </div>
            </div>
            @@include("../partials/docs-footer.html")
         </div>
      </main>

      <!-- Scripts -->

      @@include("../partials/scripts.html")

      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
