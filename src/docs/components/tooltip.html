<!doctype html>
<html lang="en">
   <head>
      @@include("../../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      @@include("../../partials/head/head-links.html")
      <title>Tooltips - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Main wrapper start-->
      <main class="docs-main-wrapper">
         @@include("../../partials/docs-navbar.html", { "classList": "w-100 fixed-top bg-white" })
         <!-- left sidebar -->
         @@include("../../partials/docs-sidenav.html")
         <!-- Wrapper  start-->
         <div class="docs-wrapper">
            <div class="docs-content">
               <!-- Content start-->
               <div class="container">
                  <div class="row">
                     <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        <div class="mb-5" id="intro">
                           <h1>Tooltips</h1>
                           <p class="mb-0">
                              Documentation and examples for adding custom Bootstrap tooltips with CSS and JavaScript using CSS3 for animations and data-attributes for local title storage.
                           </p>
                        </div>
                     </div>
                  </div>
                  <!--Example start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h2 class="text-truncate h5 mb-0">Examples</h2>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-example-tooltip-preview-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-example-tooltip-preview"
                                    role="tab"
                                    aria-controls="pills-example-tooltip-preview"
                                    aria-selected="true">
                                    <span class="lh-1"><i class="bi bi-eye"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Preview</span>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a
                                    class="nav-link"
                                    id="pills-example-tooltip-code-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-example-tooltip-code"
                                    role="tab"
                                    aria-controls="pills-example-tooltip-code"
                                    aria-selected="false">
                                    <span class="lh-1"><i class="bi bi-code"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Code</span>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-example-tooltip-preview" role="tabpanel" aria-labelledby="pills-example-tooltip-preview-tab">
                                 <!-- Tooltip on top -->
                                 <button type="button" class="btn btn-secondary" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Tooltip on top">Tooltip on top</button>

                                 <!-- Tooltip on right -->
                                 <button type="button" class="btn btn-secondary" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-title="Tooltip on right">Tooltip on right</button>
                                 <!-- Tooltip on bottom -->
                                 <button type="button" class="btn btn-secondary" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="Tooltip on bottom">Tooltip on bottom</button>
                                 <!-- Tooltip on left -->
                                 <button type="button" class="btn btn-secondary" data-bs-toggle="tooltip" data-bs-placement="left" data-bs-title="Tooltip on left">Tooltip on left</button>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-example-tooltip-code" role="tabpanel" aria-labelledby="pills-example-tooltip-code-tab">
                                 <pre class="language-markup" tabindex="0"><code class="language-markup"><span class="token comment">&lt;!-- Tooltip on top --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>btn btn-secondary<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-toggle</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>tooltip<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-placement</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>top<span class="token punctuation">"</span></span>
  <span class="token attr-name">data-bs-title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Tooltip on top<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  Tooltip on top
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>

<span class="token comment">&lt;!-- Tooltip on right --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>btn btn-secondary<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-toggle</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>tooltip<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-placement</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>right<span class="token punctuation">"</span></span>
  <span class="token attr-name">data-bs-title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Tooltip on right<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  Tooltip on right
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!-- Tooltip on bottom --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>btn btn-secondary<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-toggle</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>tooltip<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-placement</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bottom<span class="token punctuation">"</span></span>
  <span class="token attr-name">data-bs-title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Tooltip on bottom<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  Tooltip on bottom
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!-- Tooltip on left --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>btn btn-secondary<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-toggle</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>tooltip<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-placement</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>left<span class="token punctuation">"</span></span>
  <span class="token attr-name">data-bs-title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Tooltip on left<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  Tooltip on left
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Example end-->
                  <!--Custom tooltip start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h2 class="text-truncate h5 mb-0">Examples</h2>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-custom-tooltip-preview-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-custom-tooltip-preview"
                                    role="tab"
                                    aria-controls="pills-custom-tooltip-preview"
                                    aria-selected="true">
                                    <span class="lh-1"><i class="bi bi-eye"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Preview</span>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a
                                    class="nav-link"
                                    id="pills-custom-tooltip-code-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-custom-tooltip-code"
                                    role="tab"
                                    aria-controls="pills-custom-tooltip-code"
                                    aria-selected="false">
                                    <span class="lh-1"><i class="bi bi-code"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Code</span>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-custom-tooltip-preview" role="tabpanel" aria-labelledby="pills-custom-tooltip-preview-tab">
                                 <!-- Custom tooltip-->
                                 <button
                                    type="button"
                                    class="btn btn-secondary"
                                    data-bs-toggle="tooltip"
                                    data-bs-placement="top"
                                    data-bs-custom-class="custom-tooltip"
                                    data-bs-title="This top tooltip is themed via CSS variables.">
                                    Custom tooltip
                                 </button>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-custom-tooltip-code" role="tabpanel" aria-labelledby="pills-custom-tooltip-code-tab">
                                 <pre class="language-markup" tabindex="0"><code class="language-markup"><span class="token comment">&lt;!-- Custom tooltip--&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>btn btn-secondary<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-toggle</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>tooltip<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-placement</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>top<span class="token punctuation">"</span></span>
  <span class="token attr-name">data-bs-custom-class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>custom-tooltip<span class="token punctuation">"</span></span>
  <span class="token attr-name">data-bs-title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>This top tooltip is themed via CSS variables.<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  Custom tooltip
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Custom tooltip end-->
               </div>
               <!-- Content end-->
            </div>
            @@include("../../partials/docs-footer.html")
         </div>
         <!-- Wrapper  end-->
      </main>
      <!-- Main wrapper end-->
      <!-- Scripts -->
      @@include("../../partials/scripts.html")

      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
