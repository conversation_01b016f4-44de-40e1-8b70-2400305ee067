<!doctype html>
<html lang="en">
   <head>
      @@include("../../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      @@include("../../partials/head/head-links.html")
      <title>Alert - Responsive Website Template | Block</title>
   </head>

   <body>
      <!--Main wrapper start-->
      <main class="docs-main-wrapper">
         @@include("../../partials/docs-navbar.html", { "classList": "w-100 fixed-top bg-white" })
         <!-- left sidebar -->
         @@include("../../partials/docs-sidenav.html")
         <!--Wrapper start-->
         <div class="docs-wrapper">
            <div class="docs-content">
               <!--Content start-->
               <div class="container">
                  <div class="row">
                     <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        <div class="mb-5" id="intro">
                           <h1>Alert</h1>
                           <p class="mb-0">Provide contextual feedback messages for typical user actions with the handful of available and flexible alert messages.</p>
                        </div>
                     </div>
                  </div>
                  <!--Alerts start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h2 class="text-truncate h5 mb-0">Alerts</h2>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-altrt-preview-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-altrt-preview"
                                    role="tab"
                                    aria-controls="pills-altrt-preview"
                                    aria-selected="true">
                                    <span class="lh-1"><i class="bi bi-eye"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Preview</span>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a class="nav-link" id="pills-altrt-code-tab" data-bs-toggle="pill" href="#pills-altrt-code" role="tab" aria-controls="pills-altrt-code" aria-selected="false">
                                    <span class="lh-1"><i class="bi bi-code"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Code</span>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-altrt-preview" role="tabpanel" aria-labelledby="pills-altrt-preview-tab">
                                 <!--Primary alert -->
                                 <div class="alert alert-primary" role="alert">A simple primary alert—check it out!</div>
                                 <!--Secondary alert -->
                                 <div class="alert alert-secondary" role="alert">A simple secondary alert—check it out!</div>
                                 <!--Success alert-->
                                 <div class="alert alert-success" role="alert">A simple success alert—check it out!</div>
                                 <!--Danger alert-->
                                 <div class="alert alert-danger" role="alert">A simple danger alert—check it out!</div>
                                 <!--Warning alert-->
                                 <div class="alert alert-warning" role="alert">A simple warning alert—check it out!</div>
                                 <!--Info alert-->
                                 <div class="alert alert-info" role="alert">A simple info alert—check it out!</div>
                                 <!--Light alert-->
                                 <div class="alert alert-light" role="alert">A simple light alert—check it out!</div>
                                 <!--Dark alert -->
                                 <div class="alert alert-dark mb-0" role="alert">A simple dark alert—check it out!</div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-altrt-code" role="tabpanel" aria-labelledby="pills-altrt-code-tab">
                                 <pre class="language-markup" tabindex="0"><code class="language-markup"><span class="token comment">&lt;!--Primary alert --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-primary<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  A simple primary alert—check it out!
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!--Secondary alert --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-secondary<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  A simple secondary alert—check it out!
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!--Success alert--&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-success<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  A simple success alert—check it out!
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!--Danger alert--&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-danger<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  A simple danger alert—check it out!
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!--Warning alert--&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-warning<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  A simple warning alert—check it out!
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!--Info alert--&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-info<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  A simple info alert—check it out!
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!--Light alert--&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-light<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  A simple light alert—check it out!
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!--Dark alert --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-dark<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  A simple dark alert—check it out!
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Alerts end-->
                  <!--Live alert start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h2 class="text-truncate h5 mb-0">Live Alert</h2>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-live-alert-preview-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-live-alert-preview"
                                    role="tab"
                                    aria-controls="pills-live-alert-preview"
                                    aria-selected="true">
                                    <span class="lh-1"><i class="bi bi-eye"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Preview</span>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a
                                    class="nav-link"
                                    id="pills-live-alert-code-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-live-alert-code"
                                    role="tab"
                                    aria-controls="pills-live-alert-code"
                                    aria-selected="false">
                                    <span class="lh-1"><i class="bi bi-code"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Code</span>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-live-alert-preview" role="tabpanel" aria-labelledby="pills-live-alert-preview-tab">
                                 <!--Live alert-->
                                 <div id="liveAlertPlaceholder"></div>
                                 <button type="button" class="btn btn-primary" id="liveAlertBtn">Show live alert</button>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-live-alert-code" role="tabpanel" aria-labelledby="pills-live-alert-code-tab">
                                 <pre class="language-markup" tabindex="0"><code class="language-markup"><span class="token comment">&lt;!--Live alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>liveAlertPlaceholder<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>btn btn-primary<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>liveAlertBtn<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Show live alert<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Live alert end-->
                  <!--Link color start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h2 class="text-truncate h5 mb-0">Link Color</h2>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-link-color-example-preview-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-link-color-example-preview"
                                    role="tab"
                                    aria-controls="pills-link-color-example-preview"
                                    aria-selected="true">
                                    <span class="lh-1"><i class="bi bi-eye"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Preview</span>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a
                                    class="nav-link"
                                    id="pills-link-color-example-code-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-link-color-example-code"
                                    role="tab"
                                    aria-controls="pills-link-color-example-code"
                                    aria-selected="false">
                                    <span class="lh-1"><i class="bi bi-code"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Code</span>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-link-color-example-preview" role="tabpanel" aria-labelledby="pills-link-color-example-preview-tab">
                                 <!--Primary alert-->
                                 <div class="alert alert-primary" role="alert">
                                    A simple primary alert with
                                    <a href="#" class="alert-link">an example link</a>
                                    . Give it a click if you like.
                                 </div>
                                 <!--Secondary alert-->
                                 <div class="alert alert-secondary" role="alert">
                                    A simple secondary alert with
                                    <a href="#" class="alert-link">an example link</a>
                                    . Give it a click if you like.
                                 </div>
                                 <!--Success alert-->
                                 <div class="alert alert-success" role="alert">
                                    A simple success alert with
                                    <a href="#" class="alert-link">an example link</a>
                                    . Give it a click if you like.
                                 </div>
                                 <!--Danger alert-->
                                 <div class="alert alert-danger" role="alert">
                                    A simple danger alert with
                                    <a href="#" class="alert-link">an example link</a>
                                    . Give it a click if you like.
                                 </div>
                                 <!--Warining alert-->
                                 <div class="alert alert-warning" role="alert">
                                    A simple warning alert with
                                    <a href="#" class="alert-link">an example link</a>
                                    . Give it a click if you like.
                                 </div>
                                 <!--Info alert-->
                                 <div class="alert alert-info" role="alert">
                                    A simple info alert with
                                    <a href="#" class="alert-link">an example link</a>
                                    . Give it a click if you like.
                                 </div>
                                 <!--Light alert-->
                                 <div class="alert alert-light" role="alert">
                                    A simple light alert with
                                    <a href="#" class="alert-link">an example link</a>
                                    . Give it a click if you like.
                                 </div>
                                 <!--Dark alert-->
                                 <div class="alert alert-dark mb-0" role="alert">
                                    A simple dark alert with
                                    <a href="#" class="alert-link">an example link</a>
                                    . Give it a click if you like.
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-link-color-example-code" role="tabpanel" aria-labelledby="pills-link-color-example-code-tab">
                                 <pre class="language-markup" tabindex="0"><code class="language-markup"><span class="token comment">&lt;!--Primary alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-primary<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    A simple primary alert with <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>an example link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>. Give it a click if you like.
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token comment">&lt;!--Secondary alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-secondary<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    A simple secondary alert with <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>an example link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>. Give it a click if you like.
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token comment">&lt;!--Success alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-success<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    A simple success alert with <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>an example link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>. Give it a click if you like.
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token comment">&lt;!--Danger alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-danger<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    A simple danger alert with <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>an example link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>. Give it a click if you like.
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token comment">&lt;!--Warining alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-warning<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    A simple warning alert with <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>an example link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>. Give it a click if you like.
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token comment">&lt;!--Info alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-info<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    A simple info alert with <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>an example link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>. Give it a click if you like.
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token comment">&lt;!--Light alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-light<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    A simple light alert with <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>an example link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>. Give it a click if you like.
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token comment">&lt;!--Dark alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-dark<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    A simple dark alert with <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>an example link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>. Give it a click if you like.
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Link color end-->
                  <!--Additional content start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h2 class="text-truncate h5 mb-0">Additional Content</h2>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-additional-content-preview-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-additional-content-preview"
                                    role="tab"
                                    aria-controls="pills-additional-content-preview"
                                    aria-selected="true">
                                    <span class="lh-1"><i class="bi bi-eye"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Preview</span>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a
                                    class="nav-link"
                                    id="pills-additional-content-code-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-additional-content-code"
                                    role="tab"
                                    aria-controls="pills-additional-content-code"
                                    aria-selected="false">
                                    <span class="lh-1"><i class="bi bi-code"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Code</span>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-additional-content-preview" role="tabpanel" aria-labelledby="pills-additional-content-preview-tab">
                                 <!--Additional content-->
                                 <div class="alert alert-success mb-0" role="alert">
                                    <h4 class="alert-heading">Well done!</h4>
                                    <p>
                                       Aww yeah, you successfully read this important alert message. This example text is going to run a bit longer so that you can see how spacing within an alert
                                       works with this kind of content.
                                    </p>
                                    <hr />
                                    <p class="mb-0">Whenever you need to, be sure to use margin utilities to keep things nice and tidy.</p>
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-additional-content-code" role="tabpanel" aria-labelledby="pills-additional-content-code-tab">
                                 <pre class="language-markup" tabindex="0"><code class="language-markup"><span class="token comment">&lt;!--Additional content--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-success <span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h4</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert-heading<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Well done!<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h4</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span>Aww yeah, you successfully read this important alert message. This example text is going to
      run a bit longer so that you can see how spacing within an alert works with this kind of
      content.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>hr</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Whenever you need to, be sure to use margin utilities to keep things nice and
      tidy.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Additional content end-->
                  <!--Icons start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h2 class="text-truncate h5 mb-0">Icons</h2>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-icon-preview-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-icon-preview"
                                    role="tab"
                                    aria-controls="pills-icon-preview"
                                    aria-selected="true">
                                    <span class="lh-1"><i class="bi bi-eye"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Preview</span>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a class="nav-link" id="pills-icon-code-tab" data-bs-toggle="pill" href="#pills-icon-code" role="tab" aria-controls="pills-icon-code" aria-selected="false">
                                    <span class="lh-1"><i class="bi bi-code"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Code</span>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>

                     <div class="col-md-12">
                        <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                           <div class="tab-pane tab-example-preview fade show active" id="pills-icon-preview" role="tabpanel" aria-labelledby="pills-icon-preview-tab">
                              <!--Primary alert-->
                              <div class="alert alert-primary d-flex align-items-center" role="alert">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-info-circle-fill me-2" viewBox="0 0 16 16">
                                    <path
                                       d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
                                 </svg>
                                 <div>An example alert with an icon</div>
                              </div>

                              <!--Success alert-->
                              <div class="alert alert-success d-flex align-items-center" role="alert">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-check-circle-fill me-2" viewBox="0 0 16 16">
                                    <path
                                       d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                 </svg>
                                 <div>An example success alert with an icon</div>
                              </div>

                              <!--Warning alert-->
                              <div class="alert alert-warning d-flex align-items-center" role="alert">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-exclamation-triangle-fill me-2" viewBox="0 0 16 16">
                                    <path
                                       d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z" />
                                 </svg>
                                 <div>An example warning alert with an icon</div>
                              </div>

                              <!--Danger alert-->
                              <div class="alert alert-danger d-flex align-items-center mb-0" role="alert">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-exclamation-triangle-fill me-2" viewBox="0 0 16 16">
                                    <path
                                       d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z" />
                                 </svg>
                                 <div>An example danger alert with an icon</div>
                              </div>
                           </div>
                           <div class="tab-pane tab-example-code fade" id="pills-icon-code" role="tabpanel" aria-labelledby="pills-icon-code-tab">
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span class="token comment">&lt;!--Primary alert--&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-primary d-flex align-items-center<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span>
      <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-info-circle-fill me-2<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
      An example alert with an icon
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>


  <span class="token comment">&lt;!--Success alert--&gt;</span>

  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-success d-flex align-items-center<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span>
      <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-check-circle-fill me-2<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
      An example success alert with an icon
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>


  <span class="token comment">&lt;!--Warning alert--&gt;</span>

  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-warning d-flex align-items-center<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span>
      <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-exclamation-triangle-fill me-2<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
      An example warning alert with an icon
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>

  <span class="token comment">&lt;!--Danger alert--&gt;</span>

  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-danger d-flex align-items-center<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span>
      <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-exclamation-triangle-fill me-2<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
      An example danger alert with an icon
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Icons end-->
                  <!--Dismissing start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h2 class="text-truncate h5 mb-0">Dismissing</h2>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-dismissing-preview-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-dismissing-preview"
                                    role="tab"
                                    aria-controls="pills-dismissing-preview"
                                    aria-selected="true">
                                    <span class="lh-1"><i class="bi bi-eye"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Preview</span>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a
                                    class="nav-link"
                                    id="pills-dismissing-code-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-dismissing-code"
                                    role="tab"
                                    aria-controls="pills-dismissing-code"
                                    aria-selected="false">
                                    <span class="lh-1"><i class="bi bi-code"></i></span>
                                    <span class="ms-2 d-none d-lg-block">Code</span>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-dismissing-preview" role="tabpanel" aria-labelledby="pills-dismissing-preview-tab">
                                 <!--Disimissing -->
                                 <div class="alert alert-warning alert-dismissible fade show mb-0" role="alert">
                                    <strong>Holy guacamole!</strong>
                                    You should check in on some of those fields below.
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-dismissing-code" role="tabpanel" aria-labelledby="pills-dismissing-code-tab">
                                 <pre class="language-markup" tabindex="0"><code class="language-markup"><span class="token comment">&lt;!--Disimissing --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert alert-warning alert-dismissible fade show mb-0<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>strong</span><span class="token punctuation">&gt;</span></span>Holy guacamole!<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>strong</span><span class="token punctuation">&gt;</span></span>
    You should check in on some of those fields below.
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>btn-close<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-dismiss</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>alert<span class="token punctuation">"</span></span>
        <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Close<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Dismissing end-->
               </div>
               <!--Content end-->
            </div>
            @@include("../../partials/docs-footer.html")
         </div>
         <!--Wrapper end-->
      </main>
      <!--Main wrapper end-->
      <!-- Scripts -->
      @@include("../../partials/scripts.html")

      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
