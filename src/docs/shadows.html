<!doctype html>
<html lang="en">
   <head>
      @@include("../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      @@include("../partials/head/head-links.html")

      <title>Shadows - Responsive Website Template | Block</title>
   </head>

   <body>
      <!--Main wrapper start-->
      <main class="docs-main-wrapper">
         @@include("../partials/docs-navbar.html")
         <!-- left sidebar -->
         @@include("../partials/docs-sidenav.html")
         <!--Wrapper start-->
         <div class="docs-wrapper">
            <div class="docs-content">
               <div class="container">
                  <!--Content start-->

                  <div class="row">
                     <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        <div class="mb-5">
                           <h1 class="mb-2">Shadows</h1>
                           <p class="mb-0 lead text-muted">Add or remove shadows to elements with box-shadow utilities.</p>
                        </div>
                     </div>
                  </div>
                  <!-- Flush -->
                  <div class="row">
                     <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        <div class="mb-4">
                           <h2 class="mb-1">Example</h2>
                           <p class="mb-0">
                              While shadows on components are disabled by default in Bootstrap and can be enabled via
                              <code>$enable-shadows</code>
                              , you can also quickly add or remove a shadow with our
                              <code>box-shadow</code>
                              utility classes. Includes support for
                              <code>.shadow-none</code>
                              and three default sizes (which have associated variables to match).
                           </p>
                        </div>
                        <!-- Card -->
                        <div class="mb-10">
                           <ul class="nav nav-line-bottom mb-3" id="pills-tab-shadows" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-shadows-design-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-shadows-design"
                                    role="tab"
                                    aria-controls="pills-shadows-design"
                                    aria-selected="true">
                                    Design
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a class="nav-link" id="pills-shadows-html-tab" data-bs-toggle="pill" href="#pills-shadows-html" role="tab" aria-controls="pills-shadows-html" aria-selected="false">
                                    HTML
                                 </a>
                              </li>
                           </ul>
                           <!-- Tab content -->
                           <div class="tab-content" id="pills-tabContent-shadows">
                              <div class="tab-pane tab-example-design fade show active" id="pills-shadows-design" role="tabpanel" aria-labelledby="pills-shadows-design-tab">
                                 <div class="shadow-none p-3 mb-5 bg-light rounded">No shadow</div>
                                 <div class="shadow-sm p-3 mb-5 bg-white rounded">Small shadow</div>
                                 <div class="shadow p-3 mb-5 bg-white rounded">Regular shadow</div>
                                 <div class="shadow-lg p-3 mb-5 bg-white rounded">Larger shadow</div>
                              </div>
                              <div class="tab-pane tab-example-html fade" id="pills-shadows-html" role="tabpanel" aria-labelledby="pills-shadows-html-tab">
                                 <pre><code class="language-markup"> <span class="token comment">&lt;!-- shadows --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>shadow-none p-3 mb-5 bg-light rounded<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>No shadow<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>shadow-sm p-3 mb-5 bg-white rounded<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Small shadow<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>shadow p-3 mb-5 bg-white rounded<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Regular shadow<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>shadow-lg p-3 mb-5 bg-white rounded<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Larger shadow<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- Shadows -->
                  <!--Content end-->
               </div>
            </div>
            @@include("../partials/docs-footer.html")
         </div>
         <!--Wrapper end-->
      </main>
      <!--Main wrapper end-->
      <!-- Scripts -->

      @@include("../partials/scripts.html")

      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
