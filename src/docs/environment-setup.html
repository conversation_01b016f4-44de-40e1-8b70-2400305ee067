<!doctype html>
<html lang="en">
   <head>
      @@include("../partials/head/meta.html") @@include("../partials/head/head-links.html")

      <title>Environment Setup - Responsive Website Template | Block</title>
   </head>

   <body>
      <!--Main wrapper start-->
      <main class="docs-main-wrapper">
         @@include("../partials/docs-navbar.html")
         <!-- left sidebar -->
         @@include("../partials/docs-sidenav.html")
         <!--Wrapper start-->
         <div class="docs-wrapper">
            <div class="docs-content">
               <!--Content start-->
               <div class="container">
                  <div class="row">
                     <div class="col-lg-10 col-12">
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div class="mb-5" id="intro">
                                 <h1 class="mb-2">Environment Setup</h1>
                                 <p class="mb-0 lead text-muted">To get started with Block Theme, the only requirement is a Node Js, Gulp, SASS & Bootstrap environment.</p>
                              </div>
                           </div>
                        </div>
                        <div class="row">
                           <div class="col-md-12 col-12">
                              <div class="mb-6">
                                 <div class="alert alert-warning" role="alert">
                                    Please note: this step is absolutely optional. It aims for advanced user who want to speed up development process with professional grade Front-End toolset shiped
                                    with Block. If you are not familiar with these tools and have no time to dive in you can still use plain HTML / CSS / JS to customize Block. Files you need are
                                    located inside Block/dist folder. In this case you can skip this and next sections. Happy coding :)
                                 </div>
                              </div>
                              <div class="mb-5">
                                 <h2>Modern development toolset.</h2>
                                 <p class="mb-0">Below are the tools which we use to building a Block theme. For more detail of the toolset, go to their official website.</p>
                              </div>
                              <div class="mb-5">
                                 <div class="row">
                                    <div class="col-lg-6 col-12">
                                       <div class="mb-6">
                                          <div class="mb-4">
                                             <img src="../assets/images/docs/path-nodejs.svg" alt="" class="avatar-md" />
                                          </div>
                                          <h3>Node Js</h3>
                                          <p>
                                             <a href="https://nodejs.org/en/" target="_blank">Node.js</a>
                                             is an open-source, cross-platform, back-end JavaScript runtime environment that runs on the V8 engine and executes JavaScript code outside a web browser.
                                          </p>
                                       </div>
                                    </div>
                                    <div class="col-lg-6 col-12">
                                       <div class="mb-6">
                                          <div class="mb-4">
                                             <img src="../assets/images/docs/path-gulp.svg" alt="" class="avatar-md" />
                                          </div>
                                          <h3>Gulp Js</h3>
                                          <p>
                                             A toolkit to automate & enhance your workflow.
                                             <a href="https://gulpjs.com/" target="_blank">Gulp</a>
                                             is an open-source JavaScript toolkit used as a streaming build system in front-end web development.
                                          </p>
                                       </div>
                                    </div>
                                    <div class="col-lg-6 col-12">
                                       <div class="mb-6">
                                          <div class="mb-4">
                                             <img src="../assets/images/docs/path-sass.svg" alt="" class="avatar-md" />
                                          </div>
                                          <h3>SASS</h3>
                                          <p>
                                             <a href="https://sass-lang.com/" target="_blank">Sass</a>
                                             (short for syntactically awesome style sheets) is a preprocessor scripting language that is interpreted or compiled into Cascading Style Sheets (CSS).
                                          </p>
                                       </div>
                                    </div>
                                    <div class="col-lg-6 col-12">
                                       <div class="mb-6">
                                          <div class="mb-4">
                                             <img src="../assets/images/docs/path-bootstrap.svg" alt="" class="avatar-md" />
                                          </div>
                                          <h3>Bootstrap 5</h3>
                                          <p>
                                             <a href="https://getbootstrap.com/" target="_blank">Bootstrap</a>
                                             is a free and open-source CSS framework directed at responsive, mobile-first front-end web development. Block is built on 5 and heavily depends on it.
                                          </p>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <!--Content end-->
            </div>
            @@include("../partials/docs-footer.html")
         </div>
         <!--Wrapper end-->
      </main>
      <!--Main wrapper end-->
      <!-- Scripts -->
      @@include("../partials/scripts.html")

      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
