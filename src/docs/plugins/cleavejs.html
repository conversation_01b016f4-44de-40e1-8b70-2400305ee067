<!doctype html>
<html lang="en">
   <head>
      @@include("../../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />

      @@include("../../partials/head/head-links.html")
      <title>Cleave.js Input - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Main wrapper start-->
      <main class="docs-main-wrapper">
         @@include("../../partials/docs-navbar.html", { "classList": "w-100 fixed-top bg-white" })
         <!-- left sidebar -->
         @@include("../../partials/docs-sidenav.html")
         <!-- Wrapper  start-->
         <div class="docs-wrapper">
            <div class="docs-content">
               <!-- Content start-->
               <div class="container">
                  <div class="row">
                     <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        <div class="mb-5" id="intro">
                           <h1>Cleave.js</h1>
                           <p>Cleave.js has a simple purpose: to help you format input text content automatically.</p>
                           <div class="alert alert-warning d-flex gap-3">
                              <div>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-info-circle" viewBox="0 0 16 16">
                                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                    <path
                                       d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0" />
                                 </svg>
                              </div>
                              <div>
                                 Requires JS.
                                 <div class="my-2 fw-semibold">assets/js/vendors/cleave-function.js</div>

                                 <div class="mt-1">
                                    Note that this component requires the use of the third-party
                                    <a href="https://github.com/nosir/cleave.js" class="alert-link" target="_blank">cleave.js</a>
                                    plugin.
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>

                  <!--Form control end-->
                  <!--Credit input start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h3 class="text-truncate h5 mb-0">Credit card</h3>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <div>
                              <ul class="nav nav-pills nav-custom-pill" id="pills-tabOne" role="tablist">
                                 <li class="nav-item">
                                    <a
                                       class="nav-link active"
                                       id="pills-card-preview-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-card-preview"
                                       role="tab"
                                       aria-controls="pills-card-preview"
                                       aria-selected="true">
                                       <span class="lh-1"><i class="bi bi-eye"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Preview</span>
                                    </a>
                                 </li>
                                 <li class="nav-item">
                                    <a class="nav-link" id="pills-card-code-tab" data-bs-toggle="pill" href="#pills-card-code" role="tab" aria-controls="pills-card-code" aria-selected="false">
                                       <span class="lh-1"><i class="bi bi-code"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Code</span>
                                    </a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabOneContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-card-preview" role="tabpanel" aria-labelledby="pills-card-preview-tab">
                                 <div class="mb-3" style="width: 300px">
                                    <label for="cc-mask" class="form-label">Credit card number</label>
                                    <input type="text" class="form-control input-credit-card" id="cc-mask" placeholder="xxxx-xxxx-xxxx-xxxx" required="" />
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-card-code" role="tabpanel" aria-labelledby="pills-card-code-tab">
                                 <pre
                                    class="language-markup"
                                    tabindex="0"><code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>cc-mask<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Credit card number<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-credit-card<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>cc-mask<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>xxxx-xxxx-xxxx-xxxx<span class="token punctuation">"</span></span> <span class="token attr-name">required</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- Credit input end-->
                  <!--Phone input start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h3 class="text-truncate h5 mb-0">Phone number</h3>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <div>
                              <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                                 <li class="nav-item">
                                    <a
                                       class="nav-link active"
                                       id="pills-phone-preview-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-phone-preview"
                                       role="tab"
                                       aria-controls="pills-phone-preview"
                                       aria-selected="true">
                                       <span class="lh-1"><i class="bi bi-eye"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Preview</span>
                                    </a>
                                 </li>
                                 <li class="nav-item">
                                    <a class="nav-link" id="pills-phone-code-tab" data-bs-toggle="pill" href="#pills-phone-code" role="tab" aria-controls="pills-phone-code" aria-selected="false">
                                       <span class="lh-1"><i class="bi bi-code"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Code</span>
                                    </a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-phone-preview" role="tabpanel" aria-labelledby="pills-phone-preview-tab">
                                 <div class="mb-3" style="width: 300px">
                                    <label for="phone" class="form-label">Phone number</label>
                                    <input type="text" class="form-control input-phone" id="phone" placeholder="+1 4XX XXX XXXX" required="" />
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-phone-code" role="tabpanel" aria-labelledby="pills-phone-code-tab">
                                 <pre
                                    class="language-markup"
                                    tabindex="0"><code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>phone<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Phone number<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-phone<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>phone<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>+1 4XX XXX XXXX<span class="token punctuation">"</span></span> <span class="token attr-name">required</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Phone input end-->
                  <!--Date input start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h3 class="text-truncate h5 mb-0">Date formatting</h3>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <div>
                              <ul class="nav nav-pills nav-custom-pill" id="pills-tabThree" role="tablist">
                                 <li class="nav-item">
                                    <a
                                       class="nav-link active"
                                       id="pills-date-preview-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-date-preview"
                                       role="tab"
                                       aria-controls="pills-date-preview"
                                       aria-selected="true">
                                       <span class="lh-1"><i class="bi bi-eye"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Preview</span>
                                    </a>
                                 </li>
                                 <li class="nav-item">
                                    <a class="nav-link" id="pills-date-code-tab" data-bs-toggle="pill" href="#pills-date-code" role="tab" aria-controls="pills-date-code" aria-selected="false">
                                       <span class="lh-1"><i class="bi bi-code"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Code</span>
                                    </a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabThreeContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-date-preview" role="tabpanel" aria-labelledby="pills-date-preview-tab">
                                 <div class="mb-3" style="width: 300px">
                                    <label for="dateMonth" class="form-label">Date</label>
                                    <input type="text" class="form-control input-date-formatting" id="dateMonth" placeholder="YYYY-MM-DD" required="" />
                                 </div>
                                 <div class="mb-3" style="width: 300px">
                                    <label for="dateMonthOne" class="form-label">Date</label>
                                    <input type="text" class="form-control input-date-formate" id="dateMonthOne" placeholder="MM/YY" required="" />
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-date-code" role="tabpanel" aria-labelledby="pills-date-code-tab">
                                 <pre
                                    class="language-markup"
                                    tabindex="0"><code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>dateMonth<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Date<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-date-formatting<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>dateMonth<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>YYYY-MM-DD<span class="token punctuation">"</span></span> <span class="token attr-name">required</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>dateMonthOne<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Date<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-date-formate<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>dateMonthOne<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>MM/YY<span class="token punctuation">"</span></span> <span class="token attr-name">required</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Date input end-->
                  <!--Time input start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h3 class="text-truncate h5 mb-0">Time formatting</h3>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <div>
                              <ul class="nav nav-pills nav-custom-pill" id="pills-tabFour" role="tablist">
                                 <li class="nav-item">
                                    <a
                                       class="nav-link active"
                                       id="pills-timimg-preview-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-timimg-preview"
                                       role="tab"
                                       aria-controls="pills-timimg-preview"
                                       aria-selected="true">
                                       <span class="lh-1"><i class="bi bi-eye"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Preview</span>
                                    </a>
                                 </li>
                                 <li class="nav-item">
                                    <a class="nav-link" id="pills-timimg-code-tab" data-bs-toggle="pill" href="#pills-timimg-code" role="tab" aria-controls="pills-timimg-code" aria-selected="false">
                                       <span class="lh-1"><i class="bi bi-code"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Code</span>
                                    </a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabFourContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-timimg-preview" role="tabpanel" aria-labelledby="pills-timimg-preview-tab">
                                 <div class="mb-3" style="width: 300px">
                                    <label for="timeMonth" class="form-label">Time</label>
                                    <input type="tel" class="form-control input-time-formatting" id="timeMonth" placeholder="HH:MM:SS" required="" />
                                 </div>
                                 <div class="mb-3" style="width: 300px">
                                    <label for="timeMonthOne" class="form-label">Time</label>
                                    <input type="tel" class="form-control input-time-formate" id="timeMonthOne" placeholder="HH:MM" required="" />
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-timimg-code" role="tabpanel" aria-labelledby="pills-timimg-code-tab">
                                 <pre
                                    class="language-markup"
                                    tabindex="0"><code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>timeMonth<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Time<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>tel<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-time-formatting<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>timeMonth<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>HH:MM:SS<span class="token punctuation">"</span></span> <span class="token attr-name">required</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>timeMonthOne<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Time<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>tel<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-time-formate<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>timeMonthOne<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>HH:MM<span class="token punctuation">"</span></span> <span class="token attr-name">required</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Time input end-->
                  <!--Numeral input start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h3 class="text-truncate h5 mb-0">Numeral formatting</h3>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <div>
                              <ul class="nav nav-pills nav-custom-pill" id="pills-tabFive" role="tablist">
                                 <li class="nav-item">
                                    <a
                                       class="nav-link active"
                                       id="pills-nurmeral-preview-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-nurmeral-preview"
                                       role="tab"
                                       aria-controls="pills-nurmeral-preview"
                                       aria-selected="true">
                                       <span class="lh-1"><i class="bi bi-eye"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Preview</span>
                                    </a>
                                 </li>
                                 <li class="nav-item">
                                    <a
                                       class="nav-link"
                                       id="pills-nurmeral-code-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-nurmeral-code"
                                       role="tab"
                                       aria-controls="pills-nurmeral-code"
                                       aria-selected="false">
                                       <span class="lh-1"><i class="bi bi-code"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Code</span>
                                    </a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabFiveContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-nurmeral-preview" role="tabpanel" aria-labelledby="pills-nurmeral-preview-tab">
                                 <div class="mb-3" style="width: 300px">
                                    <label for="numeral" class="form-label">Numeral formatting</label>
                                    <input type="text" class="form-control input-numeral-formatting" id="numeral" placeholder="Enter numeral" required />
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-nurmeral-code" role="tabpanel" aria-labelledby="pills-nurmeral-code-tab">
                                 <pre
                                    class="language-markup"
                                    tabindex="0"><code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>numeral<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Numeral formatting<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-numeral-formatting<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>numeral<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Enter numeral<span class="token punctuation">"</span></span> <span class="token attr-name">required</span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Numeral input end-->
                  <!--Numeral input start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h3 class="text-truncate h5 mb-0">Custom options</h3>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <div>
                              <ul class="nav nav-pills nav-custom-pill" id="pills-tabSix" role="tablist">
                                 <li class="nav-item">
                                    <a
                                       class="nav-link active"
                                       id="pills-custom-preview-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-custom-preview"
                                       role="tab"
                                       aria-controls="pills-custom-preview"
                                       aria-selected="true">
                                       <span class="lh-1"><i class="bi bi-eye"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Preview</span>
                                    </a>
                                 </li>
                                 <li class="nav-item">
                                    <a class="nav-link" id="pills-custom-code-tab" data-bs-toggle="pill" href="#pills-custom-code" role="tab" aria-controls="pills-custom-code" aria-selected="false">
                                       <span class="lh-1"><i class="bi bi-code"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Code</span>
                                    </a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabSixContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-custom-preview" role="tabpanel" aria-labelledby="pills-custom-preview-tab">
                                 <div class="mb-3" style="width: 300px">
                                    <label for="block" class="form-label">Block option</label>
                                    <input type="text" class="form-control input-block" id="block" placeholder="Blocks: [4, 3, 3, 4]" required />
                                 </div>
                                 <div class="mb-3" style="width: 300px">
                                    <label for="delimiter" class="form-label">Delimiter option</label>
                                    <input type="text" class="form-control input-delimiter" id="delimiter" placeholder="Delimiter: '.'" required />
                                 </div>
                                 <div class="mb-3" style="width: 300px">
                                    <label for="delimiters" class="form-label">Delimiters option</label>
                                    <input type="text" class="form-control input-delimiters" id="delimiters" placeholder="Delimiter: '.''.''-'" required />
                                 </div>
                                 <div class="mb-3" style="width: 300px">
                                    <label for="prefix" class="form-label">Prefix option</label>
                                    <input type="text" class="form-control input-prefix" id="prefix" placeholder="PRFIX" required />
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-custom-code" role="tabpanel" aria-labelledby="pills-custom-code-tab">
                                 <pre
                                    class="language-markup"
                                    tabindex="0"><code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>block<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Block option<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-block<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>block<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Blocks: [4, 3, 3, 4]<span class="token punctuation">"</span></span> <span class="token attr-name">required</span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>delimiter<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Delimiter option<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-delimiter<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>delimiter<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Delimiter: '.'<span class="token punctuation">"</span></span> <span class="token attr-name">required</span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>delimiters<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Delimiters option<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-delimiters<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>delimiters<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Delimiter: '.''.''-'<span class="token punctuation">"</span></span> <span class="token attr-name">required</span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>prefix<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Prefix option<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control input-prefix<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>prefix<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>PRFIX<span class="token punctuation">"</span></span> <span class="token attr-name">required</span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Numeral input end-->
               </div>
               <!-- Content end-->
            </div>
            @@include("../../partials/docs-footer.html")
         </div>
         <!-- Wrapper  start-->
      </main>
      <!-- Main wrapper start-->
      <!-- Scripts -->
      @@include("../../partials/scripts.html")

      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/node_modules/cleave.js/dist/cleave.min.js"></script>
      <script src="@@webRoot/node_modules/cleave.js/dist/addons/cleave-phone.i18n.js"></script>
      <script src="@@webRoot/assets/js/vendors/cleave-function.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
