<!doctype html>
<html lang="en">
   <head>
      @@include("../../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      <link rel="stylesheet" href="@@webRoot/node_modules/intl-tel-input/build/css/intlTelInput.min.css" />
      @@include("../../partials/head/head-links.html")
      <title>International Telephone Input - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Main wrapper start-->
      <main class="docs-main-wrapper">
         @@include("../../partials/docs-navbar.html", { "classList": "w-100 fixed-top bg-white" })
         <!-- left sidebar -->
         @@include("../../partials/docs-sidenav.html")
         <!-- Wrapper  start-->
         <div class="docs-wrapper">
            <div class="docs-content">
               <!-- Content start-->
               <div class="container">
                  <div class="row">
                     <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        <div class="mb-5" id="intro">
                           <h1>International Telephone Input</h1>
                           <p>
                              A JavaScript plugin for entering and validating international telephone numbers. It takes a regular input field, adds a searchable country dropdown, auto-detects the
                              user's country, displays a relevant placeholder number, formats the number as you type, and provides comprehensive validation methods.
                           </p>
                           <div class="alert alert-warning d-flex gap-3">
                              <div>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-info-circle" viewBox="0 0 16 16">
                                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                    <path
                                       d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0" />
                                 </svg>
                              </div>
                              <div>
                                 Requires JS.
                                 <div class="my-2 fw-semibold">
                                    assets/scss/vendors/intl/_intl.scss
                                    <br />
                                    assets/js/vendors/intl-tel.js
                                 </div>

                                 <div class="mt-1">
                                    Note that this component requires the use of the third-party
                                    <a href="https://github.com/jackocnr/intl-tel-input" class="alert-link" target="_blank">intl-tel-input</a>
                                    plugin.
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Form control start-->
                  <div class="mb-3">
                     <h2 class="mb-0">Basic Demo</h2>
                  </div>
                  <!--Form control end-->
                  <!--Input start-->
                  <div class="border rounded mb-lg-7 mb-5">
                     <div class="row align-items-center py-2 px-3">
                        <div class="col-lg-8 col-xl-9 col-7">
                           <div>
                              <h3 class="text-truncate h5 mb-0">Input</h3>
                           </div>
                        </div>
                        <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                           <div>
                              <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                                 <li class="nav-item">
                                    <a
                                       class="nav-link active"
                                       id="pills-input-preview-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-input-preview"
                                       role="tab"
                                       aria-controls="pills-input-preview"
                                       aria-selected="true">
                                       <span class="lh-1"><i class="bi bi-eye"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Preview</span>
                                    </a>
                                 </li>
                                 <li class="nav-item">
                                    <a class="nav-link" id="pills-input-code-tab" data-bs-toggle="pill" href="#pills-input-code" role="tab" aria-controls="pills-input-code" aria-selected="false">
                                       <span class="lh-1"><i class="bi bi-code"></i></span>
                                       <span class="ms-2 d-none d-lg-block">Code</span>
                                    </a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-12">
                           <div class="tab-content border-top p-3" id="pills-tabTwoContent">
                              <div class="tab-pane tab-example-preview fade show active" id="pills-input-preview" role="tabpanel" aria-labelledby="pills-input-preview-tab">
                                 <div class="mb-3" style="width: 300px">
                                    <label for="phone" class="form-label visually-hidden">Phone</label>
                                    <input type="tel" name="phone" placeholder="" class="form-control w-100" required autocomplete="phone" id="phone" />

                                    <div class="invalid-feedback">Please enter Phone.</div>
                                 </div>
                              </div>
                              <div class="tab-pane tab-example-code fade" id="pills-input-code" role="tabpanel" aria-labelledby="pills-input-code-tab">
                                 <pre
                                    class="language-markup"
                                    tabindex="0"><code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>label</span> <span class="token attr-name">for</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>phone<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-label visually-hidden<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Phone<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>label</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>tel<span class="token punctuation">"</span></span> <span class="token attr-name">name</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>phone<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>form-control w-100<span class="token punctuation">"</span></span> <span class="token attr-name">required</span> <span class="token attr-name">autocomplete</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>phone<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>phone<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>invalid-feedback<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Please enter Phone.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!--Input end-->
               </div>
               <!-- Content end-->
            </div>
            @@include("../../partials/docs-footer.html")
         </div>
         <!-- Wrapper  start-->
      </main>
      <!-- Main wrapper start-->
      <!-- Scripts -->
      @@include("../../partials/scripts.html")

      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/node_modules/intl-tel-input/build/js/intlTelInput.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/intl-tel.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
