<!doctype html>
<html lang="en">
   <head>
      @@include("../partials/head/meta.html") @@include("../partials/head/head-links.html")

      <title>Introduction - Responsive Website Template | Block</title>
   </head>

   <body>
      <!--Main wrapper start-->
      <main class="docs-main-wrapper">
         @@include("../partials/docs-navbar.html")
         <!-- left sidebar -->
         @@include("../partials/docs-sidenav.html")
         <!--Wrapper start-->
         <div class="docs-wrapper">
            <div class="docs-content">
               <!--Content start-->
               <div class="container">
                  <div class="row">
                     <div class="col-lg-10 col-12">
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div class="mb-5" id="intro">
                                 <h1 class="mb-2">Introduction</h1>
                                 <p>
                                    Block is beautifully designed, expertly crafted components UI kit for building a high-quality website and web apps using web technologies — HTML, CSS, and
                                    JavaScript — with integrations of the world’s most popular framework
                                    <a href="https://getbootstrap.com/" target="_blank" rel="nofollow">Bootstrap</a>
                                    .
                                 </p>
                              </div>
                           </div>
                        </div>
                        <div class="row">
                           <div class="col-md-12 col-12">
                              <div class="mb-5">
                                 <p class="fs-4 mb-0">Guide will help you get started with a Block Theme, including how to run, customize, update, and integrate your theme!</p>
                                 <div class="my-6">
                                    <div class="alert alert-info" role="alert">
                                       Note: We highly recommend to check out The Guide an official Bootstrap Theme
                                       <a href="https://themes.getbootstrap.com/guide/" class="alert-link">guidelines</a>
                                       by Bootstrap core team.
                                    </div>
                                 </div>
                              </div>
                              <div class="mb-5" id="setting-up">
                                 <h2>Setting up local development</h2>
                                 <p class="mb-0">
                                    Block is built using modern development toolset. Setting up local development Like Gulp, Node js, SASS & Bootstrap SCSS.
                                    <a href="environment-setup.html">Read More</a>
                                 </p>
                              </div>
                              <div class="mb-5" id="customize-theme">
                                 <div class="mb-4">
                                    <h2>Customize your theme</h2>
                                    <p class="mb-0">
                                       Now that you’ve got your theme running and understand the compiling process, let’s discuss the proper ways to edit and customize your theme. There are two
                                       primary strategies for working with your theme.
                                    </p>
                                 </div>
                                 <div class="row">
                                    <div class="col-lg-6 col-12">
                                       <div class="card border shadow-none mb-4 mb-lg-0">
                                          <div class="card-body p-6">
                                             <div class="mb-4">
                                                <img src="../assets/images/docs/path-gulp.svg" alt="" class="avatar-lg" />
                                             </div>
                                             <h3>Working with Gulp</h3>
                                             <p>Working with source files showcases how powerful Bootstrap themes can be and the underlying design systems.</p>
                                             <a href="#" class="btn-link">Start with Gulp</a>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-lg-6 col-12">
                                       <div class="card border shadow-none mb-4 mb-lg-0">
                                          <div class="card-body p-6">
                                             <div class="mb-4">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="54" height="54" fill="currentColor" class="bi bi-folder-fill text-warning" viewBox="0 0 16 16">
                                                   <path
                                                      d="M9.828 3h3.982a2 2 0 0 1 1.992 2.181l-.637 7A2 2 0 0 1 13.174 14H2.825a2 2 0 0 1-1.991-1.819l-.637-7a1.99 1.99 0 0 1 .342-1.31L.5 3a2 2 0 0 1 2-2h3.672a2 2 0 0 1 1.414.586l.828.828A2 2 0 0 0 9.828 3zm-8.322.12C1.72 3.042 1.95 3 2.19 3h5.396l-.707-.707A1 1 0 0 0 6.172 2H2.5a1 1 0 0 0-1 .981l.006.139z" />
                                                </svg>
                                             </div>
                                             <h3>Working with compiled files</h3>
                                             <p>Simply attach the compiled CSS and JS files to an HTML page, or use an HTML page already provided in your theme.</p>
                                             <a href="#" class="btn-link">Start with compiled HTML files</a>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <!--Content end-->
            </div>
            @@include("../partials/docs-footer.html")
         </div>
         <!--Wrapper end-->
      </main>
      <!--Main wrapper end-->

      <!-- Scripts -->
      @@include("../partials/scripts.html")
      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
