<!doctype html>
<html lang="en">
   <head>
      @@include("../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      @@include("../partials/head/head-links.html")

      <title>Compiled Files - Responsive Website Template | Block</title>
   </head>

   <body>
      <!--Main wrapper start-->
      <main class="docs-main-wrapper">
         @@include("../partials/docs-navbar.html")
         <!-- left sidebar -->
         @@include("../partials/docs-sidenav.html")
         <!--Wrapper start-->
         <div class="docs-wrapper">
            <div class="docs-content">
               <!--Content start-->
               <div class="container">
                  <div class="row">
                     <div class="col-lg-10 col-12">
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div class="mb-5" id="intro">
                                 <h1 class="mb-2">Working with Compiled Files</h1>
                                 <p>Working with the compiled files generated from running your theme’s build tools is the simplest, fastest way to get started with a theme.</p>
                              </div>
                           </div>
                        </div>
                        <div class="row">
                           <div class="col-md-12 col-12">
                              <div class="mb-5" id="quickstart">
                                 <h2>Quick Start</h2>
                                 <p>Simply attach the compiled CSS and JS files to an HTML page, or use an HTML page already provided in your theme. No build tools or local servers necessary.</p>
                                 <div class="mt-3 mb-4">
                                    <div class="alert alert-info" role="alert">Note: For compiled files, Use a /dist folder.</div>
                                 </div>
                                 <p>If you’ve ever worked with Bootstrap by simply "attaching the CSS and JS", this is the same idea</p>
                              </div>
                              <div class="mb-6">
                                 <h2>CSS</h2>
                                 <p class="mb-0">
                                    Copy-paste the stylesheet
                                    <code>&lt;link&gt;</code>
                                    into your
                                    <code>&lt;head&gt;</code>
                                    before all other stylesheets to load our CSS.
                                 </p>

                                 <pre
                                    class="language-markup"
                                    tabindex="0"><code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>link</span> <span class="token attr-name">rel</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>stylesheet<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/css/theme.min.css<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                              <div class="mb-6">
                                 <h2>JS</h2>
                                 <p class="mb-0">
                                    Copy-paste the Javascript
                                    <code>&lt;script&gt;</code>
                                    into your HTML files before
                                    <code>&lt;body&gt;</code>
                                    .
                                 </p>

                                 <pre><code class="language-markup" data-copy-state="copy">&lt;script src=&quot;../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js&quot;&gt;&lt;/script&gt;
&lt;script src=&quot;../assets/js/theme.min.js&quot;&gt;&lt;/script&gt;</code></pre>
                              </div>
                              <div class="mb-6">
                                 <h2>Starter template</h2>
                                 <p class="mb-0">Starter template is a snippet code for blank HTML page. Use the below snippet as a way to quickly start any new blank page.</p>

                                 <pre><code class="language-markup" data-copy-state="copy">&lt;!doctype html&gt;
&lt;html lang=&quot;en&quot;&gt;
  &lt;head&gt;
     &lt;!-- Required meta tags --&gt;
     &lt;meta charset=&quot;utf-8 &gt;
     &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1, shrink-to-fit=no&quot;&gt;
     &lt;!-- Bootstrap CSS --&gt;
     &lt;link rel=&quot;stylesheet&quot; href=&quot;../assets/css/theme.min.css&quot;&gt;
     &lt;title&gt;Hello, world!&lt;/title&gt;
  &lt;/head&gt;
  &lt;body&gt;
      &lt;h1&gt;Hello, world!&lt;/h1&gt;
      &lt;!-- Optional JavaScript --&gt;
      &lt;!-- Bootstrap JS --&gt;
      &lt;script src=&quot;../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js&quot;&gt;&lt;/script&gt;
      &lt;script src=&quot;../assets/js/theme.min.js&quot;&gt;&lt;/script&gt;
  &lt;/body&gt;
&lt;/html&gt;</code></pre>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <!--Content end-->
            </div>
            @@include("../partials/docs-footer.html")
         </div>
         <!--Wrapper end-->
      </main>
      <!--Main wrapper end-->
      <!-- Scripts -->

      @@include("../partials/scripts.html")

      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
