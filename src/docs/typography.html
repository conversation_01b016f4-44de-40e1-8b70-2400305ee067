<!doctype html>
<html lang="en">
   <head>
      @@include("../partials/head/meta.html")
      <link href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      @@include("../partials/head/head-links.html")

      <title>Typography - Responsive Website Template | Block</title>
   </head>

   <body>
      <!--Main wrapper start-->
      <main class="docs-main-wrapper">
         @@include("../partials/docs-navbar.html")
         <!-- left sidebar -->
         @@include("../partials/docs-sidenav.html")
         <!--Wrapper start-->
         <div class="docs-wrapper">
            <div class="docs-content">
               <!--Content start-->
               <div class="container">
                  <div class="row">
                     <div class="col-lg-10 col-12">
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div class="mb-5" id="intro">
                                 <h1 class="mb-2">Typography</h1>
                                 <p class="mb-0 lead text-muted">Documentation and examples for Bootstrap typography, including global settings, headings, body text, lists, and more.</p>
                              </div>
                           </div>
                        </div>
                        <!-- Headings -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="headings" class="mb-4">
                                 <h2 class="mb-1">Headings</h2>
                                 <p>
                                    All HTML headings,
                                    <code class="highlighter-rouge">&lt;h1&gt;</code>
                                    through
                                    <code class="highlighter-rouge">&lt;h6&gt;</code>
                                    , are available.
                                 </p>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-headings" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-headings-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-headings-design"
                                          role="tab"
                                          aria-controls="pills-headings-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-headings-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-headings-html"
                                          role="tab"
                                          aria-controls="pills-headings-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Tab content -->
                                 <div class="tab-content" id="pills-tabContent-headings">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-headings-design" role="tabpanel" aria-labelledby="pills-headings-design-tab">
                                       <table class="table">
                                          <thead class="table-light">
                                             <tr>
                                                <th>Heading</th>
                                                <th>Example</th>
                                             </tr>
                                          </thead>
                                          <tbody>
                                             <tr>
                                                <td>
                                                   <p><code class="highlighter-rouge">&lt;h1&gt;&lt;/h1&gt;</code></p>
                                                </td>
                                                <td><span class="h1">h1. Bootstrap heading</span></td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <p><code class="highlighter-rouge">&lt;h2&gt;&lt;/h2&gt;</code></p>
                                                </td>
                                                <td><span class="h2">h2. Bootstrap heading</span></td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <p><code class="highlighter-rouge">&lt;h3&gt;&lt;/h3&gt;</code></p>
                                                </td>
                                                <td><span class="h3">h3. Bootstrap heading</span></td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <p><code class="highlighter-rouge">&lt;h4&gt;&lt;/h4&gt;</code></p>
                                                </td>
                                                <td><span class="h4">h4. Bootstrap heading</span></td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <p><code class="highlighter-rouge">&lt;h5&gt;&lt;/h5&gt;</code></p>
                                                </td>
                                                <td><span class="h5">h5. Bootstrap heading</span></td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <p><code class="highlighter-rouge">&lt;h6&gt;&lt;/h6&gt;</code></p>
                                                </td>
                                                <td><span class="h6">h6. Bootstrap heading</span></td>
                                             </tr>
                                          </tbody>
                                       </table>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-headings-html" role="tabpanel" aria-labelledby="pills-headings-html-tab">
                                       <pre><code class="language-markup"><span class="token comment">&lt;!-- headings --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span><span class="token punctuation">&gt;</span></span>h1. Bootstrap heading<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h2</span><span class="token punctuation">&gt;</span></span>h2. Bootstrap heading<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h2</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span><span class="token punctuation">&gt;</span></span>h3. Bootstrap heading<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h4</span><span class="token punctuation">&gt;</span></span>h4. Bootstrap heading<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h4</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h5</span><span class="token punctuation">&gt;</span></span>h5. Bootstrap heading<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h5</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h6</span><span class="token punctuation">&gt;</span></span>h6. Bootstrap heading<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h6</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Headings -->

                        <div class="mb-4">
                           <h2 class="mb-1">Customizing headings</h2>
                           <p>Use the included utility classes to recreate the small secondary heading text from Bootstrap 3.</p>
                        </div>
                        <div class="mb-10">
                           <ul class="nav nav-line-bottom mb-3" id="pills-tab-custom-headings" role="tablist">
                              <li class="nav-item">
                                 <a
                                    class="nav-link active"
                                    id="pills-custom-headings-design-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-custom-headings-design"
                                    role="tab"
                                    aria-controls="pills-custom-headings-design"
                                    aria-selected="true">
                                    Design
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a
                                    class="nav-link"
                                    id="pills-custom-headings-html-tab"
                                    data-bs-toggle="pill"
                                    href="#pills-custom-headings-html"
                                    role="tab"
                                    aria-controls="pills-custom-headings-html"
                                    aria-selected="false">
                                    HTML
                                 </a>
                              </li>
                           </ul>
                           <!-- Tab content -->
                           <div class="tab-content" id="pills-tabContent-custom-headings">
                              <div class="tab-pane tab-example-design fade show active" id="pills-custom-headings-design" role="tabpanel" aria-labelledby="pills-custom-headings-design-tab">
                                 <h3>
                                    Fancy display heading
                                    <small class="text-muted">With faded secondary text</small>
                                 </h3>
                              </div>
                              <div class="tab-pane tab-example-html fade" id="pills-custom-headings-html" role="tabpanel" aria-labelledby="pills-custom-headings-html-tab">
                                 <pre><code class="language-markup"> <span class="token comment">&lt;!-- Customizing headings --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span><span class="token punctuation">&gt;</span></span>
   Fancy display heading
   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-muted<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>With faded secondary text<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span></code></pre>
                              </div>
                           </div>
                        </div>

                        <!-- Display headings -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="display-headings" class="mb-4">
                                 <h2 class="mb-1">Display headings</h2>
                                 <p>
                                    Traditional heading elements are designed to work best in the meat of your page content. When you need a heading to stand out, consider using a
                                    <strong>display heading</strong>
                                    —a larger, slightly more opinionated heading style. Keep in mind these headings are not responsive by default, but it’s possible to enable
                                    <a href="#">responsive font sizes</a>
                                    .
                                 </p>
                              </div>
                              <!-- Card -->

                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-display-headings" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-display-headings-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-display-headings-design"
                                          role="tab"
                                          aria-controls="pills-display-headings-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-display-headings-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-display-headings-html"
                                          role="tab"
                                          aria-controls="pills-display-headings-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Tab content -->
                                 <div class="tab-content" id="pills-tabContent-display-headings">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-display-headings-design" role="tabpanel" aria-labelledby="pills-display-headings-design-tab">
                                       <table class="table">
                                          <tbody>
                                             <tr>
                                                <td class="border-top-0 ps-0"><span class="display-1">Display 1</span></td>
                                             </tr>
                                             <tr>
                                                <td class="ps-0"><span class="display-2">Display 2</span></td>
                                             </tr>
                                             <tr>
                                                <td class="ps-0"><span class="display-3">Display 3</span></td>
                                             </tr>
                                             <tr>
                                                <td class="ps-0"><span class="display-4">Display 4</span></td>
                                             </tr>
                                             <tr>
                                                <td class="ps-0"><span class="display-5">Display 5</span></td>
                                             </tr>
                                             <tr>
                                                <td class="ps-0 border-bottom-0"><span class="display-6">Display 6</span></td>
                                             </tr>
                                          </tbody>
                                       </table>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-display-headings-html" role="tabpanel" aria-labelledby="pills-display-headings-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- display headings --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>display-1<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Display 1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>display-2<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Display 2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>display-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Display 3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>display-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Display 4<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>display-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Display 5<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>display-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Display 6<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Display headings -->

                        <!-- Body Text -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="body" class="mb-4">
                                 <h2 class="mb-1">Body text</h2>
                                 <p>The body text or body copy is the text forming the main content.</p>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-body-text" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-body-text-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-body-text-design"
                                          role="tab"
                                          aria-controls="pills-body-text-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-body-text-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-body-text-html"
                                          role="tab"
                                          aria-controls="pills-body-text-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Tab content -->
                                 <div class="tab-content" id="pills-tabContent-body-text">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-body-text-design" role="tabpanel" aria-labelledby="pills-body-text-design-tab">
                                       <p>
                                          In ultricies fermentum aliquet. Pellentesque dui magna, condimentum non ullamcorper at, cursus in sem. Nunc condimentum, purus ac sagittis ultricies, metus
                                          leo pharetra mi, non vehicula felis elit et nisi. Etiam venenatis commodo libero, vel ullamcorper nibh lobortis vel. Aliquam auctor porta tortor, nec
                                          consequat nibh finibus a. Sed ultrices risus eget iaculis luctus. Mauris vel gravida magna.
                                       </p>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-body-text-html" role="tabpanel" aria-labelledby="pills-body-text-html-tab">
                                       <pre><code class="language-markup"><span class="token comment">&lt;!-- body text --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span>In ultricies fermentum aliquet. Pellentesque dui magna, condimentum non ullamcorper at, cursus in sem. Nunc condimentum, purus ac sagittis ultricies, metus leo pharetra mi, non vehicula felis elit et nisi. Etiam venenatis commodo libero, vel ullamcorper nibh lobortis vel. Aliquam auctor porta tortor, nec consequat nibh finibus a. Sed ultrices risus eget iaculis luctus. Mauris vel gravida magna.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Body Text -->

                        <!-- Lead Text -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="lead" class="mb-4">
                                 <h2 class="mb-1">Lead text</h2>
                                 <p>
                                    Make a paragraph stand out by adding
                                    <code class="highlighter-rouge">.lead</code>
                                    .
                                 </p>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-lead-text" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-lead-text-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-lead-text-design"
                                          role="tab"
                                          aria-controls="pills-lead-text-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-lead-text-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-lead-text-html"
                                          role="tab"
                                          aria-controls="pills-lead-text-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Tab content -->
                                 <div class="tab-content" id="pills-tabContent-lead-text">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-lead-text-design" role="tabpanel" aria-labelledby="pills-lead-text-design-tab">
                                       <p class="lead">This is a lead paragraph. It stands out from regular paragraphs.</p>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-lead-text-html" role="tabpanel" aria-labelledby="pills-lead-text-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- lead text --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>lead<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. Duis mollis, est non commodo luctus.
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Lead Text -->

                        <!-- Inline text elements -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="inline-text-elements" class="mb-4">
                                 <h2 class="mb-1">Inline text elements</h2>
                                 <p>Styling for common inline HTML5 elements.</p>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-inline-text-elements" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-inline-text-elements-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-inline-text-elements-design"
                                          role="tab"
                                          aria-controls="pills-inline-text-elements-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-inline-text-elements-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-inline-text-elements-html"
                                          role="tab"
                                          aria-controls="pills-inline-text-elements-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Tab content -->
                                 <div class="tab-content" id="pills-tabContent-inline-text-elements">
                                    <div
                                       class="tab-pane tab-example-design fade show active"
                                       id="pills-inline-text-elements-design"
                                       role="tabpanel"
                                       aria-labelledby="pills-inline-text-elements-design-tab">
                                       <p>
                                          You can use the mark tag to
                                          <mark>highlight</mark>
                                          text.
                                       </p>
                                       <p><del>This line of text is meant to be treated as deleted text.</del></p>
                                       <p><s>This line of text is meant to be treated as no longer accurate.</s></p>
                                       <p><ins>This line of text is meant to be treated as an addition to the document.</ins></p>
                                       <p><u>This line of text will render as underlined</u></p>
                                       <p><small>This line of text is meant to be treated as fine print.</small></p>
                                       <p><strong>This line rendered as bold text.</strong></p>
                                       <p><em>This line rendered as italicized text.</em></p>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-inline-text-elements-html" role="tabpanel" aria-labelledby="pills-inline-text-elements-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- inline text --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span>You can use the mark tag to <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>mark</span><span class="token punctuation">&gt;</span></span>highlight<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>mark</span><span class="token punctuation">&gt;</span></span> text.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>del</span><span class="token punctuation">&gt;</span></span>This line of text is meant to be treated as deleted text.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>del</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>s</span><span class="token punctuation">&gt;</span></span>This line of text is meant to be treated as no longer accurate.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>s</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ins</span><span class="token punctuation">&gt;</span></span>This line of text is meant to be treated as an addition to the document.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ins</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>u</span><span class="token punctuation">&gt;</span></span>This line of text will render as underlined<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>u</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span><span class="token punctuation">&gt;</span></span>This line of text is meant to be treated as fine print.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>strong</span><span class="token punctuation">&gt;</span></span>This line rendered as bold text.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>strong</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>em</span><span class="token punctuation">&gt;</span></span>This line rendered as italicized text.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>em</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Inline text elements -->

                        <!-- Blockquotes -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="blockquotes" class="mb-4">
                                 <h2 class="mb-1">Blockquotes</h2>
                                 <p>
                                    For quoting blocks of content from another source within your document. Wrap
                                    <code class="highlighter-rouge">&lt;blockquote class="blockquote"&gt;</code>
                                    around any
                                    <abbr title="HyperText Markup Language">HTML</abbr>
                                    as the quote.
                                 </p>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-blockquotes" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-blockquotes-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-blockquotes-design"
                                          role="tab"
                                          aria-controls="pills-blockquotes-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-blockquotes-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-blockquotes-html"
                                          role="tab"
                                          aria-controls="pills-blockquotes-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Content -->
                                 <div class="tab-content" id="pills-tabContent-blockquotes">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-blockquotes-design" role="tabpanel" aria-labelledby="pills-blockquotes-design-tab">
                                       <blockquote class="blockquote">
                                          <p>A well-known quote, contained in a blockquote element.</p>
                                       </blockquote>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-blockquotes-html" role="tabpanel" aria-labelledby="pills-blockquotes-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- blockquote --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>blockquote</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>blockquote<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span>A well-known quote, contained in a blockquote element.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>blockquote</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Blockquotes -->
                        <!-- Naming a source -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="naming-a-source" class="mb-4">
                                 <h3 class="mb-1">Naming a source</h3>
                                 <p>
                                    Add a
                                    <code class="highlighter-rouge">&lt;footer class="blockquote-footer"&gt;</code>
                                    for identifying the source. Wrap the name of the source work in
                                    <code class="highlighter-rouge">&lt;cite&gt;</code>
                                    .
                                 </p>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-naming-a-source" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-naming-a-source-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-naming-a-source-design"
                                          role="tab"
                                          aria-controls="pills-naming-a-source-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-naming-a-source-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-naming-a-source-html"
                                          role="tab"
                                          aria-controls="pills-naming-a-source-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Content -->
                                 <div class="tab-content" id="pills-tabContent-naming-a-source">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-naming-a-source-design" role="tabpanel" aria-labelledby="pills-naming-a-source-design-tab">
                                       <figure>
                                          <blockquote class="blockquote">
                                             <p>A well-known quote, contained in a blockquote element.</p>
                                          </blockquote>
                                          <figcaption class="blockquote-footer">
                                             Someone famous in
                                             <cite title="Source Title">Source Title</cite>
                                          </figcaption>
                                       </figure>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-naming-a-source-html" role="tabpanel" aria-labelledby="pills-naming-a-source-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- naming a source --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>figure</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>blockquote</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>blockquote<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span>A well-known quote, contained in a blockquote element.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>blockquote</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>figcaption</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>blockquote-footer<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    Someone famous in <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>cite</span> <span class="token attr-name">title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Source Title<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Source Title<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>cite</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>figcaption</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>figure</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Naming a source -->
                        <!-- Alignment -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="alignment" class="mb-4">
                                 <h3 class="mb-1">Alignment</h3>
                                 <p>Use text utilities as needed to change the alignment of your blockquote.</p>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-alignment" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-alignment-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-alignment-design"
                                          role="tab"
                                          aria-controls="pills-alignment-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-alignment-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-alignment-html"
                                          role="tab"
                                          aria-controls="pills-alignment-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Content -->
                                 <div class="tab-content" id="pills-tabContent-alignment">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-alignment-design" role="tabpanel" aria-labelledby="pills-alignment-design-tab">
                                       <figure class="text-center">
                                          <blockquote class="blockquote">
                                             <p>A well-known quote, contained in a blockquote element.</p>
                                          </blockquote>
                                          <figcaption class="blockquote-footer">
                                             Someone famous in
                                             <cite title="Source Title">Source Title</cite>
                                          </figcaption>
                                       </figure>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-alignment-html" role="tabpanel" aria-labelledby="pills-alignment-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- alignment --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>figure</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-center<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>blockquote</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>blockquote<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span>A well-known quote, contained in a blockquote element.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>blockquote</span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>figcaption</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>blockquote-footer<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
   Someone famous in <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>cite</span> <span class="token attr-name">title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Source Title<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Source Title<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>cite</span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>figcaption</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>figure</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Alignment -->
                        <!-- Alignment-right -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-alignment-right" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-alignment-right-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-alignment-right-design"
                                          role="tab"
                                          aria-controls="pills-alignment-right-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-alignment-right-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-alignment-right-html"
                                          role="tab"
                                          aria-controls="pills-alignment-right-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Tab content -->
                                 <div class="tab-content" id="pills-tabContent-alignment-right">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-alignment-right-design" role="tabpanel" aria-labelledby="pills-alignment-right-design-tab">
                                       <figure class="text-end">
                                          <blockquote class="blockquote">
                                             <p>A well-known quote, contained in a blockquote element.</p>
                                          </blockquote>
                                          <figcaption class="blockquote-footer">
                                             Someone famous in
                                             <cite title="Source Title">Source Title</cite>
                                          </figcaption>
                                       </figure>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-alignment-right-html" role="tabpanel" aria-labelledby="pills-alignment-right-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- alignment --&gt;</span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>figure</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-end<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>blockquote</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>blockquote<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span>A well-known quote, contained in a blockquote element.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>blockquote</span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>figcaption</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>blockquote-footer<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
   Someone famous in <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>cite</span> <span class="token attr-name">title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>Source Title<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Source Title<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>cite</span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>figcaption</span><span class="token punctuation">&gt;</span></span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>figure</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Alignment-right -->

                        <!-- Lists -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="lists" class="mb-4">
                                 <h2 class="mb-1">Lists</h2>
                                 <div class="mt-4" id="unstyled">
                                    <h3>Unstyled</h3>
                                    <p>
                                       Remove the default
                                       <code class="highlighter-rouge">list-style</code>
                                       and left margin on list items (immediate children only).
                                       <strong>This only applies to immediate children list items</strong>
                                       , meaning you will need to add the class for any nested lists as well.
                                    </p>
                                 </div>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-unstyled-list" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-unstyled-list-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-unstyled-list-design"
                                          role="tab"
                                          aria-controls="pills-unstyled-list-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-unstyled-list-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-unstyled-list-html"
                                          role="tab"
                                          aria-controls="pills-unstyled-list-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- tab Content -->
                                 <div class="tab-content" id="pills-tabContent-unstyled-list">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-unstyled-list-design" role="tabpanel" aria-labelledby="pills-unstyled-list-design-tab">
                                       <ul class="list-unstyled">
                                          <li>Lorem ipsum dolor sit amet</li>
                                          <li>Consectetur adipiscing elit</li>
                                          <li>Integer molestie lorem at massa</li>
                                          <li>Facilisis in pretium nisl aliquet</li>
                                          <li>
                                             Nulla volutpat aliquam velit
                                             <ul>
                                                <li>Phasellus iaculis neque</li>
                                                <li>Purus sodales ultricies</li>
                                                <li>Vestibulum laoreet porttitor sem</li>
                                                <li>Ac tristique libero volutpat at</li>
                                             </ul>
                                          </li>
                                          <li>Faucibus porta lacus fringilla vel</li>
                                          <li>Aenean sit amet erat nunc</li>
                                          <li>Eget porttitor lorem</li>
                                       </ul>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-unstyled-list-html" role="tabpanel" aria-labelledby="pills-unstyled-list-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- lists --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>list-unstyled<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Lorem ipsum dolor sit amet<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Consectetur adipiscing elit<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Integer molestie lorem at massa<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Facilisis in pretium nisl aliquet<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Nulla volutpat aliquam velit
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Phasellus iaculis neque<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Purus sodales ultricies<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Vestibulum laoreet porttitor sem<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Ac tristique libero volutpat at<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Faucibus porta lacus fringilla vel<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Aenean sit amet erat nunc<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span><span class="token punctuation">&gt;</span></span>Eget porttitor lorem<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Lists -->

                        <!-- Inline -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="inline" class="mb-4">
                                 <h2 class="mb-1">Inline</h2>
                                 <p>
                                    Remove a list’s bullets and apply some light
                                    <code class="highlighter-rouge">margin</code>
                                    with a combination of two classes,
                                    <code class="highlighter-rouge">.list-inline</code>
                                    and
                                    <code class="highlighter-rouge">.list-inline-item</code>
                                    .
                                 </p>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-inline-list" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-inline-list-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-inline-list-design"
                                          role="tab"
                                          aria-controls="pills-inline-list-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-inline-list-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-inline-list-html"
                                          role="tab"
                                          aria-controls="pills-inline-list-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Tab content -->
                                 <div class="tab-content" id="pills-tabContent-inline-list">
                                    <div class="tab-pane tab-example-design fade show active" id="pills-inline-list-design" role="tabpanel" aria-labelledby="pills-inline-list-design-tab">
                                       <ul class="list-inline">
                                          <li class="list-inline-item">Lorem ipsum</li>
                                          <li class="list-inline-item">Phasellus iaculis</li>
                                          <li class="list-inline-item">Nulla volutpat</li>
                                       </ul>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-inline-list-html" role="tabpanel" aria-labelledby="pills-inline-list-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- list inline --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>list-inline<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>list-inline-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Lorem ipsum<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>list-inline-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Phasellus iaculis<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>list-inline-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Nulla volutpat<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Inline -->

                        <!-- Description list alignment -->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="description-list-alignment" class="mb-4">
                                 <h2 class="mb-1">Description list alignment</h2>
                                 <p>
                                    Align terms and descriptions horizontally by using our grid system’s predefined classes (or semantic mixins). For longer terms, you can optionally add a
                                    <code class="highlighter-rouge">.text-truncate</code>
                                    class to truncate the text with an ellipsis.
                                 </p>
                              </div>
                              <!-- Card -->
                              <div class="mb-10">
                                 <ul class="nav nav-line-bottom mb-3" id="pills-tab-description-list-alignment" role="tablist">
                                    <li class="nav-item">
                                       <a
                                          class="nav-link active"
                                          id="pills-description-list-alignment-design-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-description-list-alignment-design"
                                          role="tab"
                                          aria-controls="pills-description-list-alignment-design"
                                          aria-selected="true">
                                          Design
                                       </a>
                                    </li>
                                    <li class="nav-item">
                                       <a
                                          class="nav-link"
                                          id="pills-description-list-alignment-html-tab"
                                          data-bs-toggle="pill"
                                          href="#pills-description-list-alignment-html"
                                          role="tab"
                                          aria-controls="pills-description-list-alignment-html"
                                          aria-selected="false">
                                          HTML
                                       </a>
                                    </li>
                                 </ul>
                                 <!-- Tab content -->
                                 <div class="tab-content" id="pills-tabContent-description-list-alignment">
                                    <div
                                       class="tab-pane tab-example-design fade show active"
                                       id="pills-description-list-alignment-design"
                                       role="tabpanel"
                                       aria-labelledby="pills-description-list-alignment-design-tab">
                                       <dl class="row">
                                          <dt class="col-sm-3">Description lists</dt>
                                          <dd class="col-sm-9">A description list is perfect for defining terms.</dd>
                                          <dt class="col-sm-3">Euismod</dt>
                                          <dd class="col-sm-9">
                                             <p>Vestibulum id ligula porta felis euismod semper eget lacinia odio sem nec elit.</p>
                                             <p>Donec id elit non mi porta gravida at eget metus.</p>
                                          </dd>
                                          <dt class="col-sm-3">Malesuada porta</dt>
                                          <dd class="col-sm-9">Etiam porta sem malesuada magna mollis euismod.</dd>
                                          <dt class="col-sm-3 text-truncate">Truncated term is truncated</dt>
                                          <dd class="col-sm-9">Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus.</dd>
                                          <dt class="col-sm-3">Nesting</dt>
                                          <dd class="col-sm-9">
                                             <dl class="row">
                                                <dt class="col-sm-4">Nested definition list</dt>
                                                <dd class="col-sm-8">Aenean posuere, tortor sed cursus feugiat, nunc augue blandit nunc.</dd>
                                             </dl>
                                          </dd>
                                       </dl>
                                    </div>
                                    <div class="tab-pane tab-example-html fade" id="pills-description-list-alignment-html" role="tabpanel" aria-labelledby="pills-description-list-alignment-html-tab">
                                       <pre><code class="language-markup"> <span class="token comment">&lt;!-- list alignment --&gt;</span>
 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dl</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>row<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dt</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Description lists<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dt</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dd</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-9<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>A description list is perfect for defining terms.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dd</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dt</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Euismod<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dt</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dd</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-9<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span>Vestibulum id ligula porta felis euismod semper eget lacinia odio sem nec elit.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span><span class="token punctuation">&gt;</span></span>Donec id elit non mi porta gravida at eget metus.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dd</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dt</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Malesuada porta<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dt</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dd</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-9<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Etiam porta sem malesuada magna mollis euismod.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dd</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dt</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-3 text-truncate<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Truncated term is truncated<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dt</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dd</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-9<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dd</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dt</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Nesting<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dt</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dd</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-9<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dl</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>row<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dt</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Nested definition list<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dt</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>dd</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>col-sm-8<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Aenean posuere, tortor sed cursus feugiat, nunc augue blandit nunc.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dd</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dl</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dd</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>dl</span><span class="token punctuation">&gt;</span></span></code></pre>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Description list alignment -->

                        <!-- Letter Space-->
                        <div class="row">
                           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <div id="letterspace" class="mb-4">
                                 <h2 class="mb-1">Letter Spacing</h2>
                                 <p>
                                    Change the letter spacing with
                                    <code>.ls-*</code>
                                    utilities.
                                 </p>
                              </div>
                              <!-- Card -->

                              <ul class="nav nav-line-bottom mb-3" id="pills-tab-letterspace" role="tablist">
                                 <li class="nav-item">
                                    <a
                                       class="nav-link active"
                                       id="pills-letterspace-design-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-letterspace-design"
                                       role="tab"
                                       aria-controls="pills-letterspace-design"
                                       aria-selected="true">
                                       Design
                                    </a>
                                 </li>
                                 <li class="nav-item">
                                    <a
                                       class="nav-link"
                                       id="pills-letterspace-html-tab"
                                       data-bs-toggle="pill"
                                       href="#pills-letterspace-html"
                                       role="tab"
                                       aria-controls="pills-letterspace-html"
                                       aria-selected="false">
                                       HTML
                                    </a>
                                 </li>
                              </ul>
                              <!-- Tab content -->
                              <div class="tab-content" id="pills-tabContent-letterspace">
                                 <div class="tab-pane tab-example-design fade show active" id="pills-letterspace-design" role="tabpanel" aria-labelledby="pills-letterspace-design-tab">
                                    <h3 class="ls-xs mb-3">Heading Title of Letter space</h3>
                                    <h3 class="ls-sm mb-3">Heading Title of Letter space</h3>
                                    <h3 class="ls-base mb-3">Heading Title of Letter space</h3>
                                    <h3 class="ls-md mb-3">Heading Title of Letter space</h3>
                                    <h3 class="ls-lg mb-3">Heading Title of Letter space</h3>
                                    <h3 class="ls-xl mb-3">Heading Title of Letter space</h3>
                                    <h3 class="ls-xxl mb-3">Heading Title of Letter space</h3>
                                 </div>
                                 <div class="tab-pane tab-example-html fade" id="pills-letterspace-html" role="tabpanel" aria-labelledby="pills-letterspace-html-tab">
                                    <pre> <span class="token comment">&lt;!-- letter spacing --&gt;</span>
  <code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>ls-xs<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Heading Title of Letter space<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>ls-sm<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Heading Title of Letter space<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>ls-base<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Heading Title of Letter space<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>ls-md<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Heading Title of Letter space<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>ls-lg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Heading Title of Letter space<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>ls-xl<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Heading Title of Letter space<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>ls-xxl<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Heading Title of Letter space<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span></code></pre>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Description list alignment -->
                     </div>
                  </div>
               </div>
               <!--Content end-->
            </div>
            @@include("../partials/docs-footer.html")
         </div>
         <!--Wrapper end-->
      </main>
      <!--Main wrapper end-->
      <!-- Scripts -->

      @@include("../partials/scripts.html")

      <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
      <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/sidebar-menu.js"></script>
      <script src="@@webRoot/assets/js/vendors/search.js"></script>
   </body>
</html>
